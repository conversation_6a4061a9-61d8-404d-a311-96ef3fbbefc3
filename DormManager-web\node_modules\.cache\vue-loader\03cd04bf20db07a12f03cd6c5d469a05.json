{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue?vue&type=template&id=05e4597b&scoped=true", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue", "mtime": 1749047102541}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;QACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAET,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAET,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1G,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1J,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1J,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/student/MyDormitory.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div>\n    <!-- 当前宿舍信息 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">我的宿舍信息</span>\n      </div>\n      <div v-if=\"currentDormitory.doro\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <p><strong>宿舍楼：</strong>{{ currentDormitory.dbname }}</p>\n            <p><strong>宿舍编号：</strong>{{ currentDormitory.doro }}</p>\n          </el-col>\n       \n        </el-row>\n      </div>\n      <div v-else>\n        <p style=\"color: #999;\">暂未分配宿舍</p>\n      </div>\n    </el-card>\n\n    <!-- 申请更换宿舍 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请更换宿舍</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"showApplyForm = !showApplyForm\">\n          {{ showApplyForm ? '收起' : '展开' }}\n        </el-button>\n      </div>\n      <div v-show=\"showApplyForm\">\n        <el-form :model=\"applyForm\" :rules=\"applyRules\" ref=\"applyFormRef\" label-width=\"120px\">\n          <el-form-item label=\"新宿舍楼\" prop=\"dbid2\">\n            <el-select v-model=\"applyForm.dbid2\" placeholder=\"请选择宿舍楼\" @change=\"onBuildingChange\" style=\"width: 300px;\">\n              <el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"新宿舍\" prop=\"doro2\">\n            <el-select v-model=\"applyForm.doro2\" placeholder=\"请先选择宿舍楼\" :disabled=\"!applyForm.dbid2\" style=\"width: 300px;\">\n              <el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"申请原因\" prop=\"applicationreason\">\n            <el-input type=\"textarea\" v-model=\"applyForm.applicationreason\" placeholder=\"请输入申请更换宿舍的原因\" :rows=\"4\" style=\"width: 500px;\"></el-input>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"submitApplication\" :loading=\"btnLoading\">提交申请</el-button>\n            <el-button @click=\"resetApplyForm\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n    </el-card>\n\n    <!-- 申请历史 -->\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请历史</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"getApplicationHistory\">刷新</el-button>\n      </div>\n      <el-table :data=\"applicationList\" style=\"width: 100%\" v-loading=\"listLoading\">\n        <el-table-column prop=\"submissiontime\" label=\"申请时间\" width=\"150\"></el-table-column>\n        <el-table-column label=\"原宿舍\" width=\"150\">\n          <template #default=\"scope\">\n            {{ scope.row.dbname || '宿舍楼' + scope.row.dbid }} - {{ scope.row.doro }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"目标宿舍\" width=\"150\">\n          <template #default=\"scope\">\n            {{ scope.row.dbname2 || '宿舍楼' + scope.row.dbid2 }} - {{ scope.row.doro2 }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"applicationreason\" label=\"申请原因\" show-overflow-tooltip></el-table-column>\n        <el-table-column prop=\"reviewstatus\" label=\"审核状态\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getStatusType(scope.row.reviewstatus)\">{{ scope.row.reviewstatus }}</el-tag>\n          </template>\n        </el-table-column>\n<el-table-column label=\"操作\" min-width=\"250\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n      \n      </el-table>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'MyDormitory',\n  data() {\n    return {\n      currentDormitory: {}, // 当前宿舍信息\n      showApplyForm: false, // 是否显示申请表单\n      btnLoading: false, // 提交按钮加载状态\n      listLoading: false, // 列表加载状态\n      \n      // 申请表单\n      applyForm: {\n        sno: '',\n        dbid: null, // 原宿舍楼ID\n        doro: '', // 原宿舍编号\n        dbid2: null, // 新宿舍楼ID\n        doro2: '', // 新宿舍编号\n        applicationreason: ''\n      },\n      \n      // 表单验证规则\n      applyRules: {\n        dbid2: [{ required: true, message: '请选择新宿舍楼', trigger: 'change' }],\n        doro2: [{ required: true, message: '请选择新宿舍', trigger: 'change' }],\n        applicationreason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]\n      },\n      \n      // 数据列表\n      dormbuildingList: [], // 宿舍楼列表\n      filteredDormitoryList: [], // 过滤后的宿舍列表\n      applicationList: [], // 申请历史列表\n      \n      // 学生信息\n      studentInfo: {}\n    };\n  },\n  \n  created() {\n    this.getStudentInfo();\n    this.getDormbuildingList();\n    this.getApplicationHistory();\n  },\n  \n  methods: {\n    // 获取学生信息和当前宿舍信息\n    getStudentInfo() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.studentInfo = user;\n      this.applyForm.sno = user.sno;\n\n      let url = base + \"/student/get?id=\" + user.sno;\n      request.post(url, {}).then((res) => {\n        if (res.code == 200) {\n          this.currentDormitory = res.resdata;\n          // 设置原宿舍信息\n          this.applyForm.dbid = res.resdata.dbid;\n          this.applyForm.doro = res.resdata.doro;\n        }\n      });\n    },\n    \n    // 获取宿舍楼列表\n    getDormbuildingList() {\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n      request.post(url, {}).then((res) => {\n        this.dormbuildingList = res.resdata;\n      });\n    },\n    \n    // 宿舍楼变化时的处理\n    onBuildingChange() {\n      this.applyForm.doro2 = ''; // 清空宿舍选择\n      this.updateDormitoryList();\n    },\n    \n    // 更新宿舍列表（根据宿舍楼和学生性别过滤）\n    updateDormitoryList() {\n      if (!this.applyForm.dbid2 || !this.studentInfo.sex) {\n        this.filteredDormitoryList = [];\n        return;\n      }\n\n      let para = {\n        dbid: this.applyForm.dbid2,\n        dorgender: this.studentInfo.sex\n      };\n      let url = base + \"/dormitory/listByBuildingAndGender\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          // 过滤掉当前宿舍\n          this.filteredDormitoryList = res.resdata.filter(item => \n            !(item.dbid == this.applyForm.dbid && item.doro == this.applyForm.doro)\n          );\n        } else {\n          this.filteredDormitoryList = [];\n          this.$message({\n            message: res.msg || \"获取宿舍列表失败\",\n            type: \"error\"\n          });\n        }\n      }).catch(() => {\n        this.filteredDormitoryList = [];\n        this.$message({\n          message: \"获取宿舍列表失败\",\n          type: \"error\"\n        });\n      });\n    },\n    \n    // 提交申请\n    submitApplication() {\n      this.$refs.applyFormRef.validate((valid) => {\n        if (valid) {\n          // 检查是否选择了不同的宿舍\n          if (this.applyForm.dbid == this.applyForm.dbid2 && this.applyForm.doro == this.applyForm.doro2) {\n            this.$message({\n              message: \"新宿舍不能与当前宿舍相同\",\n              type: \"warning\"\n            });\n            return;\n          }\n          \n          this.btnLoading = true;\n          let url = base + \"/dormitorychange/apply\";\n          request.post(url, this.applyForm).then((res) => {\n            if (res.code == 200) {\n              this.$message({\n                message: \"申请提交成功，请等待审核\",\n                type: \"success\"\n              });\n              this.resetApplyForm();\n              this.showApplyForm = false;\n              this.getApplicationHistory(); // 刷新申请历史\n            } else {\n              this.$message({\n                message: res.msg || \"申请提交失败\",\n                type: \"error\"\n              });\n            }\n            this.btnLoading = false;\n          }).catch(() => {\n            this.$message({\n              message: \"申请提交失败\",\n              type: \"error\"\n            });\n            this.btnLoading = false;\n          });\n        }\n      });\n    },\n    \n    // 重置申请表单\n    resetApplyForm() {\n      this.$refs.applyFormRef.resetFields();\n      this.applyForm.dbid2 = null;\n      this.applyForm.doro2 = '';\n      this.applyForm.applicationreason = '';\n      this.filteredDormitoryList = [];\n    },\n    \n    // 获取申请历史\n    getApplicationHistory() {\n      this.listLoading = true;\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      let para = {\n        sno: user ? user.sno : this.studentInfo.sno\n      };\n      let url = base + \"/dormitorychange/list?currentPage=1&pageSize=100\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          this.applicationList = res.resdata || [];\n        }\n        this.listLoading = false;\n      }).catch(() => {\n        this.listLoading = false;\n      });\n    },\n\n         // 删除宿舍更换\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/dormitorychange/del?id=\" + row.id;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n    },\n\n             \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/DormitorychangeDetail\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n    \n    // 获取状态标签类型\n    getStatusType(status) {\n      switch (status) {\n        case '待审核':\n          return 'warning';\n        case '审核通过':\n          return 'success';\n        case '审核不通过':\n          return 'danger';\n        default:\n          return 'info';\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin-bottom: 20px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n</style>\n"]}]}