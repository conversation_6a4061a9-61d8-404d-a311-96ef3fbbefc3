﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">

<el-form-item label="宿舍编号" prop="doro">
<el-select v-model="formData.doro" placeholder="请选择"  size="small">
<el-option v-for="item in dormitoryList" :key="item.doro" :label="item.doro" :value="item.doro"></el-option>
</el-select>
</el-form-item>
<el-form-item label="评分" prop="score">
<el-input v-model="formData.score" placeholder="评分"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="评语" prop="comment">
<el-input type="textarea" :rows="5" v-model="formData.comment" placeholder="评语"  size="small"></el-input>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'DormitoryscoreEdit',
  components: {
    
  },  
    data() {
      return {   
        id: '',
        isClear: false,
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          dbid: [{ required: true, message: '请选择宿舍楼', trigger: 'onchange' }],
          doro: [{ required: true, message: '请选择宿舍编号', trigger: 'onchange' }],
          score: [{ required: true, message: '请输入评分', trigger: 'blur' },
],          comment: [{ required: true, message: '请输入评语', trigger: 'blur' },
],        },

      };
    },
    created() {
    this.id = this.$route.query.id;
      this.getDatas();
      this.getdormbuildingList();
      this.getdormitoryList();
    },

 
    methods: {    

//获取列表数据
        getDatas() {
          let para = {
          };
          this.listLoading = true;
          let url = base + "/dormitoryscore/get?id=" + this.id;
          request.post(url, para).then((res) => {
            this.formData = JSON.parse(JSON.stringify(res.resdata));
            this.listLoading = false;
            
   
        this.doro = this.formData.doro;
        this.formData.doro = this.formData.doro;

          });
        },
    
        // 添加
        save() {
          this.$refs["formDataRef"].validate((valid) => { //验证表单
            if (valid) {
              let url = base + "/dormitoryscore/update";
              this.btnLoading = true;
              
          this.formData.doro = this.formData.doro==this.formData.doro?this.doro:this.formData.doro;

              request.post(url, this.formData).then((res) => { //发送请求         
                if (res.code == 200) {
                  this.$message({
                    message: "操作成功",
                    type: "success",
                    offset: 320,
                  });
                  this.$router.push({
                    path: "/DormitoryscoreManage",
                  });
                } else {
                  this.$message({
                    message:res.msg,
                    type: "error",
                    offset: 320,
                  });
                }
                this.btnLoading = false;
              });
            }
    
          });
        },
        
       // 返回
        goBack() {
          this.$router.push({
            path: "/DormitoryscoreManage",
          });
        },       
              
            
    getdormbuildingList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/dormbuilding/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.dormbuildingList = res.resdata;
      });
    },
    
      getdormitoryList() {
      var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息
      let para = {
         dbid:user.dbid,
      };
      this.listLoading = true;
      let url = base + "/dormitory/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.dormitoryList = res.resdata;
      });
    },
  
           
           
      },
}

</script>
<style scoped>
</style>
 

