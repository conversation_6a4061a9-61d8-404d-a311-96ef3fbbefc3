{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue", "mtime": 1749048107905}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["$", "name", "data", "userLname", "role", "activeMenu", "watch", "$route", "to", "from", "$nextTick", "initializeMenu", "mounted", "sessionStorage", "getItem", "methods", "off", "on", "e", "preventDefault", "$parent", "parent", "hasClass", "removeClass", "children", "slideUp", "addClass", "slideDown", "currentPath", "path", "each", "attr", "parents", "show", "exit", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeItem", "$router", "push", "catch"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n <nav class=\"pcoded-navbar\">\r\n        <div class=\"navbar-wrapper\">\r\n            <div class=\"navbar-brand header-logo\">\r\n                <a href=\"/main\" class=\"b-brand\">\r\n                    <div class=\"b-bg\">\r\n                        <i class=\"feather icon-trending-up\"></i>\r\n                    </div>\r\n                    <span class=\"b-title\">宿舍管理系统</span>\r\n                </a>\r\n\r\n            </div>\r\n            <div class=\"navbar-content scroll-div\">\r\n                <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '管理员'\">\r\n                 \r\n                   \r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                                 <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n    \r\n\r\n  \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n \r\n    \r\n\r\n     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">统计报表</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">报修类型统计</router-link></li>\r\n  <li> <router-link to=\"/total2\">维修员维修统计</router-link></li>\r\n  <li> <router-link to=\"/total3\">宿舍评分统计</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">系统管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                           \r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '学生'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                       <li> <router-link to=\"/mydormitory\">我的宿舍</router-link></li> \r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">提交离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">我的离校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                        <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">我的返校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">在线报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">我的报修</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                        <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n          \r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n     \r\n     \r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                    \r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n   \r\n \r\n    \r\n   \r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '维修员'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage2\">宿舍更换列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修类型管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍楼管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n  <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">添加报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">图表管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">图表1</router-link></li>\r\n  <li> <router-link to=\"/total2\">图表2</router-link></li>\r\n  <li> <router-link to=\"/total3\">图表3</router-link></li>\r\n  <li> <router-link to=\"/total4\">图表4</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '宿管阿姨'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                        \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                 \r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n   \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         \r\n\r\n  <li> <router-link to=\"/leaveschoolManage2\">管理离校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                   \r\n  <li> <router-link to=\"/registerinfoManage2\">管理返校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n \r\n \r\n     \r\n                                              <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                   <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n            </div>\r\n        </div>\r\n    </nav>\r\n    \r\n\r\n</template>\r\n\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n    };\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      this.activeMenu = to.name;\r\n      this.$nextTick(() => {\r\n        this.initializeMenu();\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.$nextTick(() => {\r\n      this.initializeMenu();\r\n    });\r\n  },\r\n  methods: {\r\n    initializeMenu() {\r\n      $('.nav-item.pcoded-hasmenu > a').off('click').on('click', function(e) {\r\n        e.preventDefault();\r\n        const $parent = $(this).parent();\r\n\r\n        if ($parent.hasClass('pcoded-trigger')) {\r\n          $parent.removeClass('pcoded-trigger');\r\n          $parent.children('ul').slideUp();\r\n        } else {\r\n          $('.nav-item.pcoded-hasmenu').removeClass('pcoded-trigger');\r\n          $('.nav-item.pcoded-hasmenu > ul').slideUp();\r\n          $parent.addClass('pcoded-trigger');\r\n          $parent.children('ul').slideDown();\r\n        }\r\n      });\r\n\r\n      // 初始化：根据当前路由展开对应的菜单\r\n      const currentPath = this.$route.path;\r\n      $('.pcoded-submenu a').each(function() {\r\n        if ($(this).attr('href') === currentPath) {\r\n          $(this).parents('.nav-item.pcoded-hasmenu').addClass('pcoded-trigger');\r\n          $(this).parents('ul.pcoded-submenu').show();\r\n        }\r\n      });\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem(\"userLname\");\r\n            sessionStorage.removeItem(\"role\");\r\n            _this.$router.push(\"/\");\r\n          })\r\n          .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.example-showcase .el-dropdown-link {\r\n  cursor: pointer;\r\n  color:green;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.pcoded-submenu a{\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n/*加点击效果*/\r\n.pcoded-submenu a:hover{\r\n  color: #fff;\r\n  color: #ff6600;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";AA6cA,OAAOA,CAAA,MAAO,QAAQ;AAEtB,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,IAAI,CAAE;IACpB,CAAC;EACH,CAAC;;EACDC,KAAK,EAAE;IACLC,MAAMA,CAACC,EAAE,EAAEC,IAAI,EAAE;MACf,IAAI,CAACJ,UAAS,GAAIG,EAAE,CAACP,IAAI;MACzB,IAAI,CAACS,SAAS,CAAC,MAAM;QACnB,IAAI,CAACC,cAAc,CAAC,CAAC;MACvB,CAAC,CAAC;IACJ;EACF,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACT,SAAQ,GAAIU,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACV,IAAG,GAAIS,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IAC1C,IAAI,CAACJ,SAAS,CAAC,MAAM;MACnB,IAAI,CAACC,cAAc,CAAC,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EACDI,OAAO,EAAE;IACPJ,cAAcA,CAAA,EAAG;MACfX,CAAC,CAAC,8BAA8B,CAAC,CAACgB,GAAG,CAAC,OAAO,CAAC,CAACC,EAAE,CAAC,OAAO,EAAE,UAASC,CAAC,EAAE;QACrEA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,MAAMC,OAAM,GAAIpB,CAAC,CAAC,IAAI,CAAC,CAACqB,MAAM,CAAC,CAAC;QAEhC,IAAID,OAAO,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE;UACtCF,OAAO,CAACG,WAAW,CAAC,gBAAgB,CAAC;UACrCH,OAAO,CAACI,QAAQ,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,CAAC;QAClC,OAAO;UACLzB,CAAC,CAAC,0BAA0B,CAAC,CAACuB,WAAW,CAAC,gBAAgB,CAAC;UAC3DvB,CAAC,CAAC,+BAA+B,CAAC,CAACyB,OAAO,CAAC,CAAC;UAC5CL,OAAO,CAACM,QAAQ,CAAC,gBAAgB,CAAC;UAClCN,OAAO,CAACI,QAAQ,CAAC,IAAI,CAAC,CAACG,SAAS,CAAC,CAAC;QACpC;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,WAAU,GAAI,IAAI,CAACrB,MAAM,CAACsB,IAAI;MACpC7B,CAAC,CAAC,mBAAmB,CAAC,CAAC8B,IAAI,CAAC,YAAW;QACrC,IAAI9B,CAAC,CAAC,IAAI,CAAC,CAAC+B,IAAI,CAAC,MAAM,MAAMH,WAAW,EAAE;UACxC5B,CAAC,CAAC,IAAI,CAAC,CAACgC,OAAO,CAAC,0BAA0B,CAAC,CAACN,QAAQ,CAAC,gBAAgB,CAAC;UACtE1B,CAAC,CAAC,IAAI,CAAC,CAACgC,OAAO,CAAC,mBAAmB,CAAC,CAACC,IAAI,CAAC,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ,CAAC;IAEDC,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,IAAIC,KAAI,GAAI,IAAI;MAChB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACIC,IAAI,CAAC,MAAM;QACV3B,cAAc,CAAC4B,UAAU,CAAC,WAAW,CAAC;QACtC5B,cAAc,CAAC4B,UAAU,CAAC,MAAM,CAAC;QACjCN,KAAK,CAACO,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;MACzB,CAAC,EACAC,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACvB;EACF;AACF,CAAC"}]}