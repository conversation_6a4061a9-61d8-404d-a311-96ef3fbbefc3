{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\leaveschool\\LeaveschoolAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\leaveschool\\LeaveschoolAdd.vue", "mtime": 1749047425393}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\leaveschool\\LeaveschoolAdd.vue"], "names": [], "mappings": ";AA4BA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEZ,CAAC;IACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACrE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3E,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5E,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAChF,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC1E,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3E,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3E,CAAC,SAAS,CAAC;;MAEL,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEV,CAAC;;;IAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACV,CAAC,EAAE,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;WACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACT,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;aACtB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;aAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;eACpD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;iBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACb,CAAC,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,CAAC;eACH,EAAE,CAAC,CAAC,CAAC,EAAE;iBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACb,CAAC,CAAC;eACJ;eACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACvB,CAAC,CAAC;WACJ;;SAEF,CAAC,CAAC;IACP,CAAC;;OAEE,CAAC,EAAE,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC;QACJ,CAAC;;;;;MAKH,CAAC;AACP", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/leaveschool/LeaveschoolAdd.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n\r\n<el-form-item label=\"离校日期\" prop=\"ldate\">\r\n<el-date-picker v-model=\"formData.ldate\" type=\"date\" placeholder=\"离校日期\"  style=\"width:50%;\" ></el-date-picker>\r\n</el-form-item>\r\n<el-form-item label=\"离校原因\" prop=\"reason\">\r\n<el-input v-model=\"formData.reason\" placeholder=\"离校原因\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"目的地\" prop=\"destination\">\r\n<el-input v-model=\"formData.destination\" placeholder=\"目的地\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"具体说明\" prop=\"note\">\r\n<el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.note\" placeholder=\"具体说明\"  size=\"small\"></el-input>\r\n</el-form-item>\r\n\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\n\r\nexport default {\r\n  name: 'LeaveschoolAdd',\r\n  components: {\r\n    \r\n  },  \r\n    data() {\r\n      return {   \r\n        uploadVisible: false, \r\n        btnLoading: false, //保存按钮加载状态     \r\n        formData: {}, //表单数据           \r\n        addrules: {\r\n          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },\r\n],          ldate: [{ required: true, message: '请输入离校日期', trigger: 'blur' },\r\n],          reason: [{ required: true, message: '请输入离校原因', trigger: 'blur' },\r\n],          destination: [{ required: true, message: '请输入目的地', trigger: 'blur' },\r\n],          note: [{ required: true, message: '请输入具体说明', trigger: 'blur' },\r\n],          lflag: [{ required: true, message: '请输入审核状态', trigger: 'blur' },\r\n],          reply: [{ required: true, message: '请输入审核回复', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n    mounted() {\r\n    \r\n    },\r\n\r\n \r\n    methods: {    \r\n   // 添加\r\n    save() {       \r\n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n           if (valid) {\r\n             let url = base + \"/leaveschool/add\";\r\n             this.btnLoading = true;\r\n             var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息\r\n             this.formData.sno = user.sno;\r\n             this.formData.lflag = \"待审核\"\r\n             request.post(url, this.formData).then((res) => { //发送请求         \r\n               if (res.code == 200) {\r\n                 this.$message({\r\n                   message: \"提交成功，请等待审核\",\r\n                   type: \"success\",\r\n                   offset: 320,\r\n                 });              \r\n                this.$router.push({\r\n                path: \"/LeaveschoolManage\",\r\n                });\r\n               } else {\r\n                 this.$message({\r\n                   message: res.msg,\r\n                   type: \"error\",\r\n                   offset: 320,\r\n                 });\r\n               }\r\n               this.btnLoading=false;\r\n             });\r\n           }        \r\n           \r\n         });\r\n    },\r\n    \r\n       // 返回\r\n        goBack() {\r\n          this.$router.push({\r\n            path: \"/LeaveschoolManage\",\r\n          });\r\n        },       \r\n              \r\n          \r\n           \r\n           \r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}