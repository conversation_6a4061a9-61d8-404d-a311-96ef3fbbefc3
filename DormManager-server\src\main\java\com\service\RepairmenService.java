package com.service;
import java.util.List;

import com.model.Repairmen;
import com.util.PageBean;

public interface RepairmenService{
	
	//查询多条记录
	public List<Repairmen> queryRepairmenList(Repairmen repairmen,PageBean page) throws Exception;
 
	//添加
	public int insertRepairmen(Repairmen repairmen) throws Exception ;
	
	//根据ID删除
	public int deleteRepairmen(String id) throws Exception ;
	
	//更新
	public int updateRepairmen(Repairmen repairmen) throws Exception ;
	
	//根据ID查询单条数据
	public Repairmen queryRepairmenById(String id) throws Exception ;
	
	//得到记录总数
	int getCount(Repairmen repairmen);

	//根据维修类型查找维修单最少的维修员
	public Repairmen findRepairmenWithLeastOrders(Integer typeid) throws Exception;

}

