{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\StudentInfo.vue?vue&type=template&id=110355ab", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\StudentInfo.vue", "mtime": 1749047616522}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\StudentInfo.vue"], "names": [], "mappings": ";IACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;IAGL,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/student/StudentInfo.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"学号\" prop=\"sno\">\r\n<el-input v-model=\"formData.sno\" placeholder=\"学号\"  style=\"width:50%;\" disabled ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"姓名\" prop=\"stname\">\r\n<el-input v-model=\"formData.stname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"性别\" prop=\"sex\">\r\n<el-radio-group v-model=\"formData.sex\" @change=\"onGenderChange\">\r\n<el-radio label=\"男\">\r\n男\r\n</el-radio>\r\n<el-radio label=\"女\">\r\n女\r\n</el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item label=\"手机号码\" prop=\"phone\">\r\n<el-input v-model=\"formData.phone\" placeholder=\"手机号码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"宿舍楼\" prop=\"dbid\">\r\n<el-select v-model=\"formData.dbid\" placeholder=\"请选择\"  size=\"small\" @change=\"onBuildingChange\">\r\n<el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"宿舍\" prop=\"doro\">\r\n<el-select v-model=\"formData.doro\" placeholder=\"请先选择宿舍楼和性别\"  size=\"small\" :disabled=\"!formData.dbid || !formData.sex\">\r\n<el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"专业\" prop=\"specialty\">\r\n<el-input v-model=\"formData.specialty\" placeholder=\"专业\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"班级\" prop=\"clsname\">\r\n<el-input v-model=\"formData.clsname\" placeholder=\"班级\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\n\r\nexport default {\r\n  name: 'StudentInfo',\r\n  components: {\r\n    \r\n  },  \r\n    data() {\r\n      return {\r\n        id: '',\r\n        isClear: false,\r\n        uploadVisible: false,\r\n        btnLoading: false, //保存按钮加载状态\r\n        formData: {}, //表单数据\r\n        dormbuildingList: [], //宿舍楼列表\r\n        dormitoryList: [], //所有宿舍列表\r\n        filteredDormitoryList: [], //过滤后的宿舍列表\r\n        addrules: {\r\n          stname: [{ required: true, message: '请输入姓名', trigger: 'blur' },\r\n],          sex: [{ required: true, message: '请输入性别', trigger: 'blur' },\r\n],          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },\r\n        { pattern: /^1[3456789]\\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },\r\n],          doro: [{ required: true, message: '请选择宿舍', trigger: 'onchange' }],\r\n          specialty: [{ required: true, message: '请输入专业', trigger: 'blur' },\r\n],          clsname: [{ required: true, message: '请输入班级', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n     created() {\r\n        var user = JSON.parse(sessionStorage.getItem(\"user\"));\r\n        this.id = user.sno;\r\n        this.getDatas();\r\n        this.getdormbuildingList();\r\n        this.getdormitoryList();\r\n      },\r\n    methods: {    \r\n\r\n//获取列表数据\r\n        getDatas() {\r\n          let para = {\r\n          };\r\n          this.listLoading = true;\r\n          let url = base + \"/student/get?id=\" + this.id;\r\n          request.post(url, para).then((res) => {\r\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n            this.listLoading = false;\r\n\r\n            this.dbid = this.formData.dbid;\r\n            this.formData.dbid = this.formData.dbname;\r\n            this.doro = this.formData.doro;\r\n            this.formData.doro = this.formData.doro;\r\n\r\n        \r\n          });\r\n        },\r\n    \r\n        // 添加\r\n        save() {\r\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n            if (valid) {\r\n              let url = base + \"/student/update\";\r\n              this.btnLoading = true;\r\n                        this.formData.dbid = this.formData.dbname==this.formData.dbid?this.dbid:this.formData.dbid;\r\n          this.formData.doro = this.formData.doro==this.formData.doro?this.doro:this.formData.doro;\r\n\r\n              request.post(url, this.formData).then((res) => { //发送请求         \r\n                if (res.code == 200) {\r\n                  this.$message({\r\n                    message: \"操作成功\",\r\n                    type: \"success\",\r\n                    offset: 320,\r\n                  });                 \r\n                } else {\r\n                  this.$message({\r\n                    message: res.msg,\r\n                    type: \"error\",\r\n                    offset: 320,\r\n                  });\r\n                }\r\n                this.btnLoading = false;\r\n              });\r\n            }\r\n    \r\n          });\r\n        },\r\n       \r\n              \r\n            \r\n    getdormbuildingList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.dormbuildingList = res.resdata;\r\n      });\r\n    },\r\n    \r\n    getdormitoryList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/dormitory/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.dormitoryList = res.resdata;\r\n      });\r\n    },\r\n\r\n    // 宿舍楼变化时的处理\r\n    onBuildingChange() {\r\n      this.formData.doro = ''; // 清空宿舍选择\r\n      this.updateDormitoryList();\r\n    },\r\n\r\n    // 性别变化时的处理\r\n    onGenderChange() {\r\n      this.formData.doro = ''; // 清空宿舍选择\r\n      this.updateDormitoryList();\r\n    },\r\n\r\n    // 更新宿舍列表\r\n    updateDormitoryList() {\r\n      if (!this.formData.dbid || !this.formData.sex) {\r\n        this.filteredDormitoryList = [];\r\n        return;\r\n      }\r\n\r\n      let para = {\r\n        dbid: this.formData.dbid,\r\n        dorgender: this.formData.sex\r\n      };\r\n      let url = base + \"/dormitory/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        if (res.code == 200) {\r\n          this.filteredDormitoryList = res.resdata;\r\n        } else {\r\n          this.filteredDormitoryList = [];\r\n          this.$message({\r\n            message: res.msg || \"获取宿舍列表失败\",\r\n            type: \"error\",\r\n            offset: 320,\r\n          });\r\n        }\r\n      }).catch(() => {\r\n        this.filteredDormitoryList = [];\r\n        this.$message({\r\n          message: \"获取宿舍列表失败\",\r\n          type: \"error\",\r\n          offset: 320,\r\n        });\r\n      });\r\n    },\r\n\r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}