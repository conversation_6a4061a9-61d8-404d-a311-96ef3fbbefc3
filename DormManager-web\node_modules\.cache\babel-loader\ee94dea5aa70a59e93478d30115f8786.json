{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeEdit.vue?vue&type=template&id=56ed1ca9", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeEdit.vue", "mtime": 1749048251871}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_select", "doro", "$event", "placeholder", "size", "_Fragment", "_renderList", "_ctx", "dormitoryList", "item", "_createBlock", "_component_el_option", "key", "value", "ftype", "_component_el_input", "title", "amount", "type", "rows", "note", "_component_el_radio_group", "status", "_component_el_radio", "_component_el_button", "onClick", "$options", "save", "loading", "btnLoading", "icon", "goBack"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeEdit.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n\r\n<el-form-item label=\"宿舍编号\" prop=\"doro\">\r\n<el-select v-model=\"formData.doro\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option v-for=\"item in dormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"费用类型\" prop=\"ftype\">\r\n<el-select v-model=\"formData.ftype\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option label=\"水费\" value=\"水费\"></el-option>\r\n<el-option label=\"电费\" value=\"电费\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"费用标题\" prop=\"title\">\r\n<el-input v-model=\"formData.title\" placeholder=\"费用标题\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"费用金额\" prop=\"amount\">\r\n<el-input v-model=\"formData.amount\" placeholder=\"费用金额\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"备注说明\" prop=\"note\">\r\n<el-input type=\"textarea\" :rows=\"5\" v-model=\"formData.note\" placeholder=\"备注说明\"  size=\"small\"></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"缴纳状态\" prop=\"status\">\r\n<el-radio-group v-model=\"formData.status\" placeholder=\"缴纳状态\"  style=\"width:50%;\" >\r\n<el-radio label=\"未缴纳\" value=\"未缴纳\"></el-radio>\r\n<el-radio label=\"已缴纳\" value=\"已缴纳\"></el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\n\r\nexport default {\r\n  name: 'WaterelectricityfeeEdit',\r\n  components: {\r\n    \r\n  },  \r\n    data() {\r\n      return {   \r\n        id: '',\r\n        isClear: false,\r\n        uploadVisible: false, \r\n        btnLoading: false, //保存按钮加载状态     \r\n        formData: {}, //表单数据           \r\n        addrules: {\r\n          dbid: [{ required: true, message: '请选择宿舍楼', trigger: 'onchange' }],\r\n          doro: [{ required: true, message: '请选择宿舍编号', trigger: 'onchange' }],\r\n          ftype: [{ required: true, message: '请选择费用类型', trigger: 'onchange' }],\r\n          title: [{ required: true, message: '请输入费用标题', trigger: 'blur' },\r\n],          amount: [{ required: true, message: '请输入费用金额', trigger: 'blur' },\r\n],          note: [{ required: true, message: '请输入备注说明', trigger: 'blur' },\r\n],          status: [{ required: true, message: '请输入缴纳状态', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n    created() {\r\n    this.id = this.$route.query.id;\r\n      this.getDatas();\r\n      this.getdormbuildingList();\r\n      this.getdormitoryList();\r\n    },\r\n\r\n \r\n    methods: {    \r\n\r\n//获取列表数据\r\n        getDatas() {\r\n          let para = {\r\n          };\r\n          this.listLoading = true;\r\n          let url = base + \"/waterelectricityfee/get?id=\" + this.id;\r\n          request.post(url, para).then((res) => {\r\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n            this.listLoading = false;\r\n            \r\n      \r\n        this.doro = this.formData.doro;\r\n        this.formData.doro = this.formData.doro;\r\n\r\n          });\r\n        },\r\n    \r\n        // 添加\r\n        save() {\r\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n            if (valid) {\r\n              let url = base + \"/waterelectricityfee/update\";\r\n              this.btnLoading = true;\r\n                        this.formData.dbid = this.formData.dbid==this.formData.dbname?this.dbid:this.formData.dbid;\r\n          this.formData.doro = this.formData.doro==this.formData.doro?this.doro:this.formData.doro;\r\n\r\n              request.post(url, this.formData).then((res) => { //发送请求         \r\n                if (res.code == 200) {\r\n                  this.$message({\r\n                    message: \"操作成功\",\r\n                    type: \"success\",\r\n                    offset: 320,\r\n                  });\r\n                  this.$router.push({\r\n                    path: \"/WaterelectricityfeeManage\",\r\n                  });\r\n                } else {\r\n                  this.$message({\r\n                    message:res.msg,\r\n                    type: \"error\",\r\n                    offset: 320,\r\n                  });\r\n                }\r\n                this.btnLoading = false;\r\n              });\r\n            }\r\n    \r\n          });\r\n        },\r\n        \r\n       // 返回\r\n        goBack() {\r\n          this.$router.push({\r\n            path: \"/WaterelectricityfeeManage\",\r\n          });\r\n        },       \r\n              \r\n            \r\n    getdormbuildingList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.dormbuildingList = res.resdata;\r\n      });\r\n    },\r\n    \r\n      getdormitoryList() {\r\n      var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息\r\n        let para = {\r\n        \r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/dormitory/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.dormitoryList = res.resdata;\r\n      });\r\n    },\r\n  \r\n           \r\n           \r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;iDA8BiC,KAAG;iDAC5B,KAAG;;;;;;;;;;uBA/BvEC,mBAAA,CAoCM,OApCNC,UAoCM,GAnCHC,YAAA,CAgCGC,kBAAA;IAhCOC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAGC,KAAK,EAAC;;sBAE/F,MAIe,CAJfR,YAAA,CAIeS,uBAAA;MAJDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAEY,CAFZX,YAAA,CAEYY,oBAAA;oBAFQT,KAAA,CAAAC,QAAQ,CAACS,IAAI;mEAAbV,KAAA,CAAAC,QAAQ,CAACS,IAAI,GAAAC,MAAA;QAAEC,WAAW,EAAC,KAAK;QAAEC,IAAI,EAAC;;0BAChD,MAA6B,E,kBAAxClB,mBAAA,CAA4GmB,SAAA,QAAAC,WAAA,CAAlFC,IAAA,CAAAC,aAAa,EAArBC,IAAI;+BAAtBC,YAAA,CAA4GC,oBAAA;YAAlEC,GAAG,EAAEH,IAAI,CAACR,IAAI;YAAGH,KAAK,EAAEW,IAAI,CAACR,IAAI;YAAGY,KAAK,EAAEJ,IAAI,CAACR;;;;;;;QAG1Fb,YAAA,CAKeS,uBAAA;MALDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAGY,CAHZX,YAAA,CAGYY,oBAAA;oBAHQT,KAAA,CAAAC,QAAQ,CAACsB,KAAK;mEAAdvB,KAAA,CAAAC,QAAQ,CAACsB,KAAK,GAAAZ,MAAA;QAAEC,WAAW,EAAC,KAAK;QAAEC,IAAI,EAAC;;0BAC5D,MAA6C,CAA7ChB,YAAA,CAA6CuB,oBAAA;UAAlCb,KAAK,EAAC,IAAI;UAACe,KAAK,EAAC;YAC5BzB,YAAA,CAA6CuB,oBAAA;UAAlCb,KAAK,EAAC,IAAI;UAACe,KAAK,EAAC;;;;;QAG5BzB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAsF,CAAtFX,YAAA,CAAsF2B,mBAAA;oBAAnExB,KAAA,CAAAC,QAAQ,CAACwB,KAAK;mEAAdzB,KAAA,CAAAC,QAAQ,CAACwB,KAAK,GAAAd,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEvDG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAuF,CAAvFX,YAAA,CAAuF2B,mBAAA;oBAApExB,KAAA,CAAAC,QAAQ,CAACyB,MAAM;mEAAf1B,KAAA,CAAAC,QAAQ,CAACyB,MAAM,GAAAf,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAExDG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAwG,CAAxGX,YAAA,CAAwG2B,mBAAA;QAA9FG,IAAI,EAAC,UAAU;QAAEC,IAAI,EAAE,CAAC;oBAAW5B,KAAA,CAAAC,QAAQ,CAAC4B,IAAI;mEAAb7B,KAAA,CAAAC,QAAQ,CAAC4B,IAAI,GAAAlB,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAEC,IAAI,EAAC;;;QAErFhB,YAAA,CAKeS,uBAAA;MALDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAGiB,CAHjBX,YAAA,CAGiBiC,yBAAA;oBAHQ9B,KAAA,CAAAC,QAAQ,CAAC8B,MAAM;mEAAf/B,KAAA,CAAAC,QAAQ,CAAC8B,MAAM,GAAApB,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;0BAC9D,MAA6C,CAA7CG,YAAA,CAA6CmC,mBAAA;UAAnCzB,KAAK,EAAC,KAAK;UAACe,KAAK,EAAC;YAC5BzB,YAAA,CAA6CmC,mBAAA;UAAnCzB,KAAK,EAAC,KAAK;UAACe,KAAK,EAAC;;;;;QAG5BzB,YAAA,CAGeS,uBAAA;wBAFf,MAAgH,CAAhHT,YAAA,CAAgHoC,oBAAA;QAArGN,IAAI,EAAC,SAAS;QAACd,IAAI,EAAC,OAAO;QAAEqB,OAAK,EAAEC,QAAA,CAAAC,IAAI;QAAGC,OAAO,EAAErC,KAAA,CAAAsC,UAAU;QAAEC,IAAI,EAAC;;0BAAiB,MAAG,C;;iDACpG1C,YAAA,CAAuFoC,oBAAA;QAA5EN,IAAI,EAAC,MAAM;QAACd,IAAI,EAAC,OAAO;QAAEqB,OAAK,EAAEC,QAAA,CAAAK,MAAM;QAAED,IAAI,EAAC;;0BAAe,MAAG,C"}]}