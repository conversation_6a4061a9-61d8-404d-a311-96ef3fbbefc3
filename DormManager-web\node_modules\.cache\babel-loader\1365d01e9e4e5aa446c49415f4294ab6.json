{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue", "mtime": 1749047102541}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "currentDormitory", "showApplyForm", "btnLoading", "listLoading", "applyForm", "sno", "dbid", "doro", "dbid2", "doro2", "<PERSON><PERSON><PERSON>", "applyRules", "required", "message", "trigger", "dormbuildingList", "filteredDormitoryList", "applicationList", "studentInfo", "created", "getStudentInfo", "getDormbuildingList", "getApplicationHistory", "methods", "user", "JSON", "parse", "sessionStorage", "getItem", "url", "post", "then", "res", "code", "resdata", "onBuildingChange", "updateDormitoryList", "sex", "para", "<PERSON><PERSON><PERSON>", "filter", "item", "$message", "msg", "type", "catch", "submitApplication", "$refs", "applyFormRef", "validate", "valid", "resetApplyForm", "resetFields", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "id", "offset", "getDatas", "handleShow", "$router", "push", "path", "query", "getStatusType", "status"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue"], "sourcesContent": ["<template>\n  <div>\n    <!-- 当前宿舍信息 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">我的宿舍信息</span>\n      </div>\n      <div v-if=\"currentDormitory.doro\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <p><strong>宿舍楼：</strong>{{ currentDormitory.dbname }}</p>\n            <p><strong>宿舍编号：</strong>{{ currentDormitory.doro }}</p>\n          </el-col>\n       \n        </el-row>\n      </div>\n      <div v-else>\n        <p style=\"color: #999;\">暂未分配宿舍</p>\n      </div>\n    </el-card>\n\n    <!-- 申请更换宿舍 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请更换宿舍</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"showApplyForm = !showApplyForm\">\n          {{ showApplyForm ? '收起' : '展开' }}\n        </el-button>\n      </div>\n      <div v-show=\"showApplyForm\">\n        <el-form :model=\"applyForm\" :rules=\"applyRules\" ref=\"applyFormRef\" label-width=\"120px\">\n          <el-form-item label=\"新宿舍楼\" prop=\"dbid2\">\n            <el-select v-model=\"applyForm.dbid2\" placeholder=\"请选择宿舍楼\" @change=\"onBuildingChange\" style=\"width: 300px;\">\n              <el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"新宿舍\" prop=\"doro2\">\n            <el-select v-model=\"applyForm.doro2\" placeholder=\"请先选择宿舍楼\" :disabled=\"!applyForm.dbid2\" style=\"width: 300px;\">\n              <el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"申请原因\" prop=\"applicationreason\">\n            <el-input type=\"textarea\" v-model=\"applyForm.applicationreason\" placeholder=\"请输入申请更换宿舍的原因\" :rows=\"4\" style=\"width: 500px;\"></el-input>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"submitApplication\" :loading=\"btnLoading\">提交申请</el-button>\n            <el-button @click=\"resetApplyForm\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n    </el-card>\n\n    <!-- 申请历史 -->\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请历史</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"getApplicationHistory\">刷新</el-button>\n      </div>\n      <el-table :data=\"applicationList\" style=\"width: 100%\" v-loading=\"listLoading\">\n        <el-table-column prop=\"submissiontime\" label=\"申请时间\" width=\"150\"></el-table-column>\n        <el-table-column label=\"原宿舍\" width=\"150\">\n          <template #default=\"scope\">\n            {{ scope.row.dbname || '宿舍楼' + scope.row.dbid }} - {{ scope.row.doro }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"目标宿舍\" width=\"150\">\n          <template #default=\"scope\">\n            {{ scope.row.dbname2 || '宿舍楼' + scope.row.dbid2 }} - {{ scope.row.doro2 }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"applicationreason\" label=\"申请原因\" show-overflow-tooltip></el-table-column>\n        <el-table-column prop=\"reviewstatus\" label=\"审核状态\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getStatusType(scope.row.reviewstatus)\">{{ scope.row.reviewstatus }}</el-tag>\n          </template>\n        </el-table-column>\n<el-table-column label=\"操作\" min-width=\"250\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n      \n      </el-table>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'MyDormitory',\n  data() {\n    return {\n      currentDormitory: {}, // 当前宿舍信息\n      showApplyForm: false, // 是否显示申请表单\n      btnLoading: false, // 提交按钮加载状态\n      listLoading: false, // 列表加载状态\n      \n      // 申请表单\n      applyForm: {\n        sno: '',\n        dbid: null, // 原宿舍楼ID\n        doro: '', // 原宿舍编号\n        dbid2: null, // 新宿舍楼ID\n        doro2: '', // 新宿舍编号\n        applicationreason: ''\n      },\n      \n      // 表单验证规则\n      applyRules: {\n        dbid2: [{ required: true, message: '请选择新宿舍楼', trigger: 'change' }],\n        doro2: [{ required: true, message: '请选择新宿舍', trigger: 'change' }],\n        applicationreason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]\n      },\n      \n      // 数据列表\n      dormbuildingList: [], // 宿舍楼列表\n      filteredDormitoryList: [], // 过滤后的宿舍列表\n      applicationList: [], // 申请历史列表\n      \n      // 学生信息\n      studentInfo: {}\n    };\n  },\n  \n  created() {\n    this.getStudentInfo();\n    this.getDormbuildingList();\n    this.getApplicationHistory();\n  },\n  \n  methods: {\n    // 获取学生信息和当前宿舍信息\n    getStudentInfo() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.studentInfo = user;\n      this.applyForm.sno = user.sno;\n\n      let url = base + \"/student/get?id=\" + user.sno;\n      request.post(url, {}).then((res) => {\n        if (res.code == 200) {\n          this.currentDormitory = res.resdata;\n          // 设置原宿舍信息\n          this.applyForm.dbid = res.resdata.dbid;\n          this.applyForm.doro = res.resdata.doro;\n        }\n      });\n    },\n    \n    // 获取宿舍楼列表\n    getDormbuildingList() {\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n      request.post(url, {}).then((res) => {\n        this.dormbuildingList = res.resdata;\n      });\n    },\n    \n    // 宿舍楼变化时的处理\n    onBuildingChange() {\n      this.applyForm.doro2 = ''; // 清空宿舍选择\n      this.updateDormitoryList();\n    },\n    \n    // 更新宿舍列表（根据宿舍楼和学生性别过滤）\n    updateDormitoryList() {\n      if (!this.applyForm.dbid2 || !this.studentInfo.sex) {\n        this.filteredDormitoryList = [];\n        return;\n      }\n\n      let para = {\n        dbid: this.applyForm.dbid2,\n        dorgender: this.studentInfo.sex\n      };\n      let url = base + \"/dormitory/listByBuildingAndGender\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          // 过滤掉当前宿舍\n          this.filteredDormitoryList = res.resdata.filter(item => \n            !(item.dbid == this.applyForm.dbid && item.doro == this.applyForm.doro)\n          );\n        } else {\n          this.filteredDormitoryList = [];\n          this.$message({\n            message: res.msg || \"获取宿舍列表失败\",\n            type: \"error\"\n          });\n        }\n      }).catch(() => {\n        this.filteredDormitoryList = [];\n        this.$message({\n          message: \"获取宿舍列表失败\",\n          type: \"error\"\n        });\n      });\n    },\n    \n    // 提交申请\n    submitApplication() {\n      this.$refs.applyFormRef.validate((valid) => {\n        if (valid) {\n          // 检查是否选择了不同的宿舍\n          if (this.applyForm.dbid == this.applyForm.dbid2 && this.applyForm.doro == this.applyForm.doro2) {\n            this.$message({\n              message: \"新宿舍不能与当前宿舍相同\",\n              type: \"warning\"\n            });\n            return;\n          }\n          \n          this.btnLoading = true;\n          let url = base + \"/dormitorychange/apply\";\n          request.post(url, this.applyForm).then((res) => {\n            if (res.code == 200) {\n              this.$message({\n                message: \"申请提交成功，请等待审核\",\n                type: \"success\"\n              });\n              this.resetApplyForm();\n              this.showApplyForm = false;\n              this.getApplicationHistory(); // 刷新申请历史\n            } else {\n              this.$message({\n                message: res.msg || \"申请提交失败\",\n                type: \"error\"\n              });\n            }\n            this.btnLoading = false;\n          }).catch(() => {\n            this.$message({\n              message: \"申请提交失败\",\n              type: \"error\"\n            });\n            this.btnLoading = false;\n          });\n        }\n      });\n    },\n    \n    // 重置申请表单\n    resetApplyForm() {\n      this.$refs.applyFormRef.resetFields();\n      this.applyForm.dbid2 = null;\n      this.applyForm.doro2 = '';\n      this.applyForm.applicationreason = '';\n      this.filteredDormitoryList = [];\n    },\n    \n    // 获取申请历史\n    getApplicationHistory() {\n      this.listLoading = true;\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      let para = {\n        sno: user ? user.sno : this.studentInfo.sno\n      };\n      let url = base + \"/dormitorychange/list?currentPage=1&pageSize=100\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          this.applicationList = res.resdata || [];\n        }\n        this.listLoading = false;\n      }).catch(() => {\n        this.listLoading = false;\n      });\n    },\n\n         // 删除宿舍更换\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/dormitorychange/del?id=\" + row.id;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n    },\n\n             \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/DormitorychangeDetail\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n    \n    // 获取状态标签类型\n    getStatusType(status) {\n      switch (status) {\n        case '待审核':\n          return 'warning';\n        case '审核通过':\n          return 'success';\n        case '审核不通过':\n          return 'danger';\n        default:\n          return 'info';\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin-bottom: 20px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n</style>\n"], "mappings": ";AAyFA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AAEtD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,gBAAgB,EAAE,CAAC,CAAC;MAAE;MACtBC,aAAa,EAAE,KAAK;MAAE;MACtBC,UAAU,EAAE,KAAK;MAAE;MACnBC,WAAW,EAAE,KAAK;MAAE;;MAEpB;MACAC,SAAS,EAAE;QACTC,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,IAAI;QAAE;QACZC,IAAI,EAAE,EAAE;QAAE;QACVC,KAAK,EAAE,IAAI;QAAE;QACbC,KAAK,EAAE,EAAE;QAAE;QACXC,iBAAiB,EAAE;MACrB,CAAC;MAED;MACAC,UAAU,EAAE;QACVH,KAAK,EAAE,CAAC;UAAEI,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAS,CAAC,CAAC;QAClEL,KAAK,EAAE,CAAC;UAAEG,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAC,CAAC;QACjEJ,iBAAiB,EAAE,CAAC;UAAEE,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MAC7E,CAAC;MAED;MACAC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,qBAAqB,EAAE,EAAE;MAAE;MAC3BC,eAAe,EAAE,EAAE;MAAE;;MAErB;MACAC,WAAW,EAAE,CAAC;IAChB,CAAC;EACH,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAC9B,CAAC;EAEDC,OAAO,EAAE;IACP;IACAH,cAAcA,CAAA,EAAG;MACf,MAAMI,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACvD,IAAI,CAACV,WAAU,GAAIM,IAAI;MACvB,IAAI,CAACpB,SAAS,CAACC,GAAE,GAAImB,IAAI,CAACnB,GAAG;MAE7B,IAAIwB,GAAE,GAAIhC,IAAG,GAAI,kBAAiB,GAAI2B,IAAI,CAACnB,GAAG;MAC9CT,OAAO,CAACkC,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAACjC,gBAAe,GAAIgC,GAAG,CAACE,OAAO;UACnC;UACA,IAAI,CAAC9B,SAAS,CAACE,IAAG,GAAI0B,GAAG,CAACE,OAAO,CAAC5B,IAAI;UACtC,IAAI,CAACF,SAAS,CAACG,IAAG,GAAIyB,GAAG,CAACE,OAAO,CAAC3B,IAAI;QACxC;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAc,mBAAmBA,CAAA,EAAG;MACpB,IAAIQ,GAAE,GAAIhC,IAAG,GAAI,gDAAgD;MACjED,OAAO,CAACkC,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAI,CAACjB,gBAAe,GAAIiB,GAAG,CAACE,OAAO;MACrC,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAC/B,SAAS,CAACK,KAAI,GAAI,EAAE,EAAE;MAC3B,IAAI,CAAC2B,mBAAmB,CAAC,CAAC;IAC5B,CAAC;IAED;IACAA,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAC,IAAI,CAAChC,SAAS,CAACI,KAAI,IAAK,CAAC,IAAI,CAACU,WAAW,CAACmB,GAAG,EAAE;QAClD,IAAI,CAACrB,qBAAoB,GAAI,EAAE;QAC/B;MACF;MAEA,IAAIsB,IAAG,GAAI;QACThC,IAAI,EAAE,IAAI,CAACF,SAAS,CAACI,KAAK;QAC1B+B,SAAS,EAAE,IAAI,CAACrB,WAAW,CAACmB;MAC9B,CAAC;MACD,IAAIR,GAAE,GAAIhC,IAAG,GAAI,oCAAoC;MACrDD,OAAO,CAACkC,IAAI,CAACD,GAAG,EAAES,IAAI,CAAC,CAACP,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB;UACA,IAAI,CAACjB,qBAAoB,GAAIgB,GAAG,CAACE,OAAO,CAACM,MAAM,CAACC,IAAG,IACjD,EAAEA,IAAI,CAACnC,IAAG,IAAK,IAAI,CAACF,SAAS,CAACE,IAAG,IAAKmC,IAAI,CAAClC,IAAG,IAAK,IAAI,CAACH,SAAS,CAACG,IAAI,CACxE,CAAC;QACH,OAAO;UACL,IAAI,CAACS,qBAAoB,GAAI,EAAE;UAC/B,IAAI,CAAC0B,QAAQ,CAAC;YACZ7B,OAAO,EAAEmB,GAAG,CAACW,GAAE,IAAK,UAAU;YAC9BC,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM;QACb,IAAI,CAAC7B,qBAAoB,GAAI,EAAE;QAC/B,IAAI,CAAC0B,QAAQ,CAAC;UACZ7B,OAAO,EAAE,UAAU;UACnB+B,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAE,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAC1C,IAAIA,KAAK,EAAE;UACT;UACA,IAAI,IAAI,CAAC9C,SAAS,CAACE,IAAG,IAAK,IAAI,CAACF,SAAS,CAACI,KAAI,IAAK,IAAI,CAACJ,SAAS,CAACG,IAAG,IAAK,IAAI,CAACH,SAAS,CAACK,KAAK,EAAE;YAC9F,IAAI,CAACiC,QAAQ,CAAC;cACZ7B,OAAO,EAAE,cAAc;cACvB+B,IAAI,EAAE;YACR,CAAC,CAAC;YACF;UACF;UAEA,IAAI,CAAC1C,UAAS,GAAI,IAAI;UACtB,IAAI2B,GAAE,GAAIhC,IAAG,GAAI,wBAAwB;UACzCD,OAAO,CAACkC,IAAI,CAACD,GAAG,EAAE,IAAI,CAACzB,SAAS,CAAC,CAAC2B,IAAI,CAAEC,GAAG,IAAK;YAC9C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACS,QAAQ,CAAC;gBACZ7B,OAAO,EAAE,cAAc;gBACvB+B,IAAI,EAAE;cACR,CAAC,CAAC;cACF,IAAI,CAACO,cAAc,CAAC,CAAC;cACrB,IAAI,CAAClD,aAAY,GAAI,KAAK;cAC1B,IAAI,CAACqB,qBAAqB,CAAC,CAAC,EAAE;YAChC,OAAO;cACL,IAAI,CAACoB,QAAQ,CAAC;gBACZ7B,OAAO,EAAEmB,GAAG,CAACW,GAAE,IAAK,QAAQ;gBAC5BC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ;YACA,IAAI,CAAC1C,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC,CAAC2C,KAAK,CAAC,MAAM;YACb,IAAI,CAACH,QAAQ,CAAC;cACZ7B,OAAO,EAAE,QAAQ;cACjB+B,IAAI,EAAE;YACR,CAAC,CAAC;YACF,IAAI,CAAC1C,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAiD,cAAcA,CAAA,EAAG;MACf,IAAI,CAACJ,KAAK,CAACC,YAAY,CAACI,WAAW,CAAC,CAAC;MACrC,IAAI,CAAChD,SAAS,CAACI,KAAI,GAAI,IAAI;MAC3B,IAAI,CAACJ,SAAS,CAACK,KAAI,GAAI,EAAE;MACzB,IAAI,CAACL,SAAS,CAACM,iBAAgB,GAAI,EAAE;MACrC,IAAI,CAACM,qBAAoB,GAAI,EAAE;IACjC,CAAC;IAED;IACAM,qBAAqBA,CAAA,EAAG;MACtB,IAAI,CAACnB,WAAU,GAAI,IAAI;MACvB,MAAMqB,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACvD,IAAIU,IAAG,GAAI;QACTjC,GAAG,EAAEmB,IAAG,GAAIA,IAAI,CAACnB,GAAE,GAAI,IAAI,CAACa,WAAW,CAACb;MAC1C,CAAC;MACD,IAAIwB,GAAE,GAAIhC,IAAG,GAAI,kDAAkD;MACnED,OAAO,CAACkC,IAAI,CAACD,GAAG,EAAES,IAAI,CAAC,CAACP,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAI,CAAChB,eAAc,GAAIe,GAAG,CAACE,OAAM,IAAK,EAAE;QAC1C;QACA,IAAI,CAAC/B,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC,CAAC0C,KAAK,CAAC,MAAM;QACb,IAAI,CAAC1C,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IAEI;IACDkD,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBd,IAAI,EAAE;MACR,CAAC,EACEb,IAAI,CAAC,MAAM;QACV,IAAI,CAAC5B,WAAU,GAAI,IAAI;QACvB,IAAI0B,GAAE,GAAIhC,IAAG,GAAI,0BAAyB,GAAI0D,GAAG,CAACI,EAAE;QACpD/D,OAAO,CAACkC,IAAI,CAACD,GAAG,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;UAC9B,IAAI,CAAC7B,WAAU,GAAI,KAAK;UAExB,IAAI,CAACuC,QAAQ,CAAC;YACZ7B,OAAO,EAAE,MAAM;YACf+B,IAAI,EAAE,SAAS;YACfgB,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAACC,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAhB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACzB,CAAC;IAGG;IACAiB,UAAUA,CAACR,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACQ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,wBAAwB;QAC7BC,KAAK,EAAE;UACJP,EAAE,EAAEJ,GAAG,CAACI;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAEL;IACAQ,aAAaA,CAACC,MAAM,EAAE;MACpB,QAAQA,MAAM;QACZ,KAAK,KAAK;UACR,OAAO,SAAS;QAClB,KAAK,MAAM;UACT,OAAO,SAAS;QAClB,KAAK,OAAO;UACV,OAAO,QAAQ;QACjB;UACE,OAAO,MAAM;MACjB;IACF;EACF;AACF,CAAC"}]}