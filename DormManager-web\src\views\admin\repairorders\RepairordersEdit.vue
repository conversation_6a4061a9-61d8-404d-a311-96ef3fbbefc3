﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="报修单号">
{{formData.no}}</el-form-item>
<el-form-item label="报修类型">
{{formData.typename}}</el-form-item>
<el-form-item label="报修主题">
{{formData.tsubject}}</el-form-item>
<el-form-item label="具体描述" prop="description">
<div v-html="formData.description"></div>
</el-form-item>
<el-form-item label="宿舍编号">
{{formData.doro}}</el-form-item>
<el-form-item label="学号">
{{formData.sno}}</el-form-item>
<el-form-item label="提交时间">
{{formData.submittime}}</el-form-item>
<el-form-item label="处理进度">
{{formData.progress}}</el-form-item>
<el-form-item label="维修员账号">
{{formData.rno}}</el-form-item>
<el-form-item label="处理结果" prop="results">
<WangEditor  ref="wangEditorRef" v-model="formData.results" :config="editorConfig"   :isClear="isClear" @change="editorChange"></WangEditor>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
import WangEditor from "../../../components/WangEditor";
export default {
  name: 'RepairordersEdit',
  components: {
    WangEditor,
  },  
    data() {
      return {   
        id: '',
        isClear: false,
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          no: [{ required: true, message: '请输入报修单号', trigger: 'blur' },
],          tsubject: [{ required: true, message: '请输入报修主题', trigger: 'blur' },
],          doro: [{ required: true, message: '请输入宿舍编号', trigger: 'blur' },
],          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },
],          progress: [{ required: true, message: '请输入处理进度', trigger: 'blur' },
],          rno: [{ required: true, message: '请输入维修员账号', trigger: 'blur' },
],        },

      };
    },
    created() {
    this.id = this.$route.query.id;
      this.getDatas();
      this.getrepairtypeList();
    },

 
    methods: {    

//获取列表数据
        getDatas() {
          let para = {
          };
          this.listLoading = true;
          let url = base + "/repairorders/get?id=" + this.id;
          request.post(url, para).then((res) => {
            this.formData = JSON.parse(JSON.stringify(res.resdata));
            this.listLoading = false;
    

          });
        },
    
        // 添加
        save() {
          this.$refs["formDataRef"].validate((valid) => { //验证表单
            if (valid) {
              let url = base + "/repairorders/update";
              this.btnLoading = true;
             this.formData.progress = '已处理';

              request.post(url, this.formData).then((res) => { //发送请求         
                if (res.code == 200) {
                  this.$message({
                    message: "操作成功",
                    type: "success",
                    offset: 320,
                  });
                  this.$router.push({
                    path: "/RepairordersManage2",
                  });
                } else {
                  this.$message({
                    message:res.msg,
                    type: "error",
                    offset: 320,
                  });
                }
                this.btnLoading = false;
              });
            }
    
          });
        },
        
       // 返回
        goBack() {
          this.$router.push({
            path: "/RepairordersManage2",
          });
        },       
              
            
    getrepairtypeList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/repairtype/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.repairtypeList = res.resdata;
      });
    },
  
           
            // 富文本编辑器
    editorChange(val) {
      this.formData.results = val;
    },
   
      },
}

</script>
<style scoped>
</style>
 

