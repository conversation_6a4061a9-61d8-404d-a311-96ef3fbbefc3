package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.RepairmenMapper;
import com.model.Repairmen;
import com.util.PageBean;
@Service
public class RepairmenServiceImpl implements RepairmenService{
        
	@Autowired
	private RepairmenMapper repairmenMapper;

	//查询多条记录
	public List<Repairmen> queryRepairmenList(Repairmen repairmen,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(repairmen, page);
		
		List<Repairmen> getRepairmen = repairmenMapper.query(map);
		
		return getRepairmen;
	}
	
	//得到记录总数
	@Override
	public int getCount(Repairmen repairmen) {
		Map<String, Object> map = getQueryMap(repairmen, null);
		int count = repairmenMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Repairmen repairmen,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(repairmen!=null){
			map.put("rno", repairmen.getRno());
			map.put("password", repairmen.getPassword());
			map.put("rnname", repairmen.getRnname());
			map.put("age", repairmen.getAge());
			map.put("phone", repairmen.getPhone());
			map.put("address", repairmen.getAddress());
			map.put("typeid", repairmen.getTypeid());
			map.put("addtime", repairmen.getAddtime());
			map.put("sort", repairmen.getSort());
			map.put("condition", repairmen.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertRepairmen(Repairmen repairmen) throws Exception {
		return repairmenMapper.insertRepairmen(repairmen);
	}

	//根据ID删除
	public int deleteRepairmen(String id) throws Exception {
		return repairmenMapper.deleteRepairmen(id);
	}

	//更新
	public int updateRepairmen(Repairmen repairmen) throws Exception {
		return repairmenMapper.updateRepairmen(repairmen);
	}
	
	//根据ID得到对应的记录
	public Repairmen queryRepairmenById(String id) throws Exception {
		Repairmen po =  repairmenMapper.queryRepairmenById(id);
		return po;
	}

	//根据维修类型查找维修单最少的维修员
	public Repairmen findRepairmenWithLeastOrders(Integer typeid) throws Exception {
		return repairmenMapper.findRepairmenWithLeastOrders(typeid);
	}
}

