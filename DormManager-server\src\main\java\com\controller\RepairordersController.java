package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/repairorders")
public class RepairordersController{
	
	@Resource
	private RepairordersService repairordersService;

	@Resource
	private RepairmenService repairmenService;

	@Resource
	private StudentService studentService;
	
	//报修列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Repairorders>> list(@RequestBody Repairorders repairorders, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = repairordersService.getCount(repairorders);
		//获取当前页记录
		List<Repairorders> repairordersList = repairordersService.queryRepairordersList(repairorders, page);
		//遍历
		for (Repairorders repairorders2 : repairordersList) {
			repairorders2.setDescription(removeHTML.Html2Text(repairorders2.getDescription()));
			repairorders2.setResults(removeHTML.Html2Text(repairorders2.getResults()));

		}

		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(repairordersList, counts, page_count);
	}
        
	//添加报修
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Repairorders repairorders, HttpServletRequest req) throws Exception {
		try {
			// 生成报修单号
			String orderNo = generateRepairOrderNo();
			repairorders.setNo(orderNo);

			// 根据报修类型自动分配维修员
			if (repairorders.getTypeid() != null) {
				Repairmen assignedRepairmen = repairmenService.findRepairmenWithLeastOrders(repairorders.getTypeid());
				if (assignedRepairmen != null) {
					repairorders.setRno(assignedRepairmen.getRno());
				}
			}

			Student student = studentService.queryStudentById(repairorders.getSno());
			repairorders.setDoro(student.getDoro());

			repairordersService.insertRepairorders(repairorders); //添加

			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}

	// 生成报修单号的方法
	private String generateRepairOrderNo() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String timestamp = sdf.format(new Date());
		// 添加随机数确保唯一性
		int random = (int)(Math.random() * 1000);
		return "BX" + timestamp + String.format("%03d", random);
	}
    
	//删除报修
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			String id = req.getParameter("id");
			repairordersService.deleteRepairorders(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改报修
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Repairorders repairorders, HttpServletRequest req) throws Exception {
		try {
			repairordersService.updateRepairorders(repairorders); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回报修详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			String id = req.getParameter("id");
			Repairorders repairorders=repairordersService.queryRepairordersById(id); //根据ID查询
			return Response.success(repairorders);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
}

