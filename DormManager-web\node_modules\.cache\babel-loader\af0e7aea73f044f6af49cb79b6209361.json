{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeManage2.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeManage2.vue", "mtime": 1749049139666}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCByZXF1ZXN0LCB7IGJhc2UgfSBmcm9tICIuLi8uLi8uLi8uLi91dGlscy9odHRwIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICd3YXRlcmVsZWN0cmljaXR5ZmVlJywKICBjb21wb25lbnRzOiB7fSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZmlsdGVyczogewogICAgICAgIC8v5YiX6KGo5p+l6K+i5Y+C5pWwCiAgICAgICAgZGJpZDogJycsCiAgICAgICAgZG9ybzogJycsCiAgICAgICAgZnR5cGU6ICcnCiAgICAgIH0sCiAgICAgIHBhZ2U6IHsKICAgICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgICAvLyDlvZPliY3pobUKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgLy8g5q+P6aG15pi+56S65p2h55uu5Liq5pWwCiAgICAgICAgdG90YWxDb3VudDogMCAvLyDmgLvmnaHnm67mlbAKICAgICAgfSwKCiAgICAgIGlzQ2xlYXI6IGZhbHNlLAogICAgICBkb3JtYnVpbGRpbmdMaXN0OiBbXSwKICAgICAgLy/lrr/oiI3mpbwKICAgICAgZG9ybWl0b3J5TGlzdDogW10sCiAgICAgIC8v5a6/6IiN57yW5Y+3CgogICAgICBsaXN0TG9hZGluZzogZmFsc2UsCiAgICAgIC8v5YiX6KGo5Yqg6L2954q25oCBCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLAogICAgICAvL+S/neWtmOaMiemSruWKoOi9veeKtuaAgQogICAgICBkYXRhbGlzdDogW10sCiAgICAgIC8v6KGo5qC85pWw5o2uCgogICAgICAvLyDmlK/ku5jnm7jlhbPmlbDmja4KICAgICAgcGF5RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIC8v5pSv5LuY5by556qX5pi+56S654q25oCBCiAgICAgIGN1cnJlbnRQYXlBbW91bnQ6IDAsCiAgICAgIC8v5b2T5YmN5pSv5LuY6YeR6aKdCiAgICAgIGN1cnJlbnRQYXlSZWNvcmQ6IG51bGwsCiAgICAgIC8v5b2T5YmN5pSv5LuY6K6w5b2VCiAgICAgIHNlbGVjdGVkUGF5TWV0aG9kOiAnJywKICAgICAgLy/pgInmi6nnmoTmlK/ku5jmlrnlvI8KICAgICAgcGF5TG9hZGluZzogZmFsc2UgLy/mlK/ku5jmjInpkq7liqDovb3nirbmgIEKICAgIH07CiAgfSwKCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0RGF0YXMoKTsKICAgIHRoaXMuZ2V0ZG9ybWJ1aWxkaW5nTGlzdCgpOwogICAgdGhpcy5nZXRkb3JtaXRvcnlMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliKDpmaTmsLTnlLXotLkKICAgIGhhbmRsZURlbGV0ZShpbmRleCwgcm93KSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuehruiupOWIoOmZpOivpeiusOW9leWQlz8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3dhdGVyZWxlY3RyaWNpdHlmZWUvZGVsP2lkPSIgKyByb3cuaWQ7CiAgICAgICAgcmVxdWVzdC5wb3N0KHVybCkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8iLAogICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgIG9mZnNldDogMzIwCiAgICAgICAgICB9KTsKICAgICAgICAgIHRoaXMuZ2V0RGF0YXMoKTsKICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8vIOWIhumhtQogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5wYWdlLmN1cnJlbnRQYWdlID0gdmFsOwogICAgICB0aGlzLmdldERhdGFzKCk7CiAgICB9LAogICAgLy/ojrflj5bliJfooajmlbDmja4KICAgIGdldERhdGFzKCkgewogICAgICB2YXIgdXNlciA9IEpTT04ucGFyc2Uoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpKTsgLy/ojrflj5bnlKjmiLfkv6Hmga8gICAKICAgICAgbGV0IHBhcmEgPSB7CiAgICAgICAgY29uZGl0aW9uOiAiIGFuZCAgYS5kb3JvIGluKHNlbGVjdCBkb3JvIGZyb20gc3R1ZGVudCB3aGVyZSBzbm8gPSAiICsgdXNlci5zbm8gKyAiKSIsCiAgICAgICAgZnR5cGU6IHRoaXMuZmlsdGVycy5mdHlwZQogICAgICB9OwogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3dhdGVyZWxlY3RyaWNpdHlmZWUvbGlzdD9jdXJyZW50UGFnZT0iICsgdGhpcy5wYWdlLmN1cnJlbnRQYWdlICsgIiZwYWdlU2l6ZT0iICsgdGhpcy5wYWdlLnBhZ2VTaXplOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5yZXNkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHRoaXMuaXNQYWdlID0gdHJ1ZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5pc1BhZ2UgPSBmYWxzZTsKICAgICAgICB9CiAgICAgICAgdGhpcy5wYWdlLnRvdGFsQ291bnQgPSByZXMuY291bnQ7CiAgICAgICAgdGhpcy5kYXRhbGlzdCA9IHJlcy5yZXNkYXRhOwogICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy/mn6Xor6IKICAgIHF1ZXJ5KCkgewogICAgICB0aGlzLmdldERhdGFzKCk7CiAgICB9LAogICAgZ2V0ZG9ybWJ1aWxkaW5nTGlzdCgpIHsKICAgICAgbGV0IHBhcmEgPSB7fTsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9kb3JtYnVpbGRpbmcvbGlzdD9jdXJyZW50UGFnZT0xJnBhZ2VTaXplPTEwMDAiOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5kb3JtYnVpbGRpbmdMaXN0ID0gcmVzLnJlc2RhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIGdldGRvcm1pdG9yeUxpc3QoKSB7CiAgICAgIGxldCBwYXJhID0ge307CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlOwogICAgICBsZXQgdXJsID0gYmFzZSArICIvZG9ybWl0b3J5L2xpc3Q/Y3VycmVudFBhZ2U9MSZwYWdlU2l6ZT0xMDAwIjsKICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZG9ybWl0b3J5TGlzdCA9IHJlcy5yZXNkYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmn6XnnIsKICAgIGhhbmRsZVNob3coaW5kZXgsIHJvdykgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogIi9XYXRlcmVsZWN0cmljaXR5ZmVlRGV0YWlsIiwKICAgICAgICBxdWVyeTogewogICAgICAgICAgaWQ6IHJvdy5pZAogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g57yW6L6RCiAgICBoYW5kbGVFZGl0KGluZGV4LCByb3cpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICIvV2F0ZXJlbGVjdHJpY2l0eWZlZUVkaXQiLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICBpZDogcm93LmlkCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDnvLTnurPmsLTnlLXotLkKICAgIGhhbmRsZVBheShpbmRleCwgcm93KSB7CiAgICAgIHRoaXMuY3VycmVudFBheVJlY29yZCA9IHJvdzsKICAgICAgdGhpcy5jdXJyZW50UGF5QW1vdW50ID0gcm93LmFtb3VudDsKICAgICAgdGhpcy5zZWxlY3RlZFBheU1ldGhvZCA9ICcnOwogICAgICB0aGlzLnBheURpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8vIOmAieaLqeaUr+S7mOaWueW8jwogICAgc2VsZWN0UGF5TWV0aG9kKG1ldGhvZCkgewogICAgICB0aGlzLnNlbGVjdGVkUGF5TWV0aG9kID0gbWV0aG9kOwogICAgfSwKICAgIC8vIOehruiupOaUr+S7mAogICAgY29uZmlybVBheW1lbnQoKSB7CiAgICAgIHRoaXMucGF5TG9hZGluZyA9IHRydWU7CgogICAgICAvLyDmqKHmi5/mlK/ku5jlpITnkIbml7bpl7QKICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3dhdGVyZWxlY3RyaWNpdHlmZWUvcGF5IjsKICAgICAgICBsZXQgcGFyYSA9IHsKICAgICAgICAgIGlkOiB0aGlzLmN1cnJlbnRQYXlSZWNvcmQuaWQsCiAgICAgICAgICBwYXlNZXRob2Q6IHRoaXMuc2VsZWN0ZWRQYXlNZXRob2QKICAgICAgICB9OwogICAgICAgIHJlcXVlc3QucG9zdCh1cmwsIHBhcmEpLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMucGF5TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiAi57y057qz5oiQ5Yqf77yBIiwKICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgb2Zmc2V0OiAzMjAKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHRoaXMucGF5RGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLmdldERhdGFzKCk7IC8vIOWIt+aWsOWIl+ihqAogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLm1zZyB8fCAi57y057qz5aSx6LSl77yM6K+36YeN6K+VIiwKICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgIG9mZnNldDogMzIwCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMucGF5TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6ICLnvLTnurPlpLHotKXvvIzor7fph43or5UiLAogICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICBvZmZzZXQ6IDMyMAogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0sIDE1MDApOyAvLyDmqKHmi58xLjXnp5LmlK/ku5jlpITnkIbml7bpl7QKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "dbid", "doro", "ftype", "page", "currentPage", "pageSize", "totalCount", "isClear", "dormbuildingList", "dormitoryList", "listLoading", "btnLoading", "datalist", "payDialogVisible", "currentPayAmount", "currentPayRecord", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "payLoading", "created", "getDatas", "getdormbuildingList", "getdormitoryList", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "id", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "user", "JSON", "parse", "sessionStorage", "getItem", "para", "condition", "sno", "resdata", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "handleEdit", "handlePay", "amount", "selectPayMethod", "method", "confirmPayment", "setTimeout", "payMethod", "code", "msg"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeManage2.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      <el-col  :span=\"24\"  style=\"padding-bottom: 0px; margin-left: 10px\">\n<el-form :inline=\"true\" :model=\"filters\" >\n\n<el-form-item label=\"费用类型\" prop=\"ftype\">\n<el-select v-model=\"filters.ftype\" placeholder=\"请选择\"  size=\"small\">\n<el-option label=\"全部\" value=\"\"></el-option>\n<el-option label=\"水费\" value=\"水费\"></el-option>\n<el-option label=\"电费\" value=\"电费\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n</el-form-item>\n </el-form>\n</el-col>\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"dbname\" label=\"宿舍楼\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"doro\" label=\"宿舍编号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"ftype\" label=\"费用类型\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"title\" label=\"费用标题\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"amount\" label=\"费用金额\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"addtime\" label=\"添加时间\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"status\" label=\"缴纳状态\"  align=\"center\"></el-table-column>\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button v-if=\"scope.row.status === '未缴纳'\" type=\"success\" size=\"mini\" @click=\"handlePay(scope.$index, scope.row)\" icon=\"el-icon-money\" style=\" padding: 3px 6px 3px 6px; margin-left: 5px;\">缴纳</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n<!-- 支付弹窗 -->\n<el-dialog title=\"水电费缴纳\" v-model=\"payDialogVisible\" width=\"400px\" center>\n  <div style=\"text-align: center;\">\n    <h3>缴纳金额：<span style=\"color: #f56c6c; font-size: 24px;\">￥{{ currentPayAmount }}</span></h3>\n    <p style=\"margin: 20px 0;\">请选择支付方式：</p>\n\n    <div style=\"display: flex; justify-content: space-around; margin: 30px 0;\">\n      <div @click=\"selectPayMethod('wechat')\"\n           :class=\"['pay-method', { 'selected': selectedPayMethod === 'wechat' }]\"\n           style=\"cursor: pointer; padding: 10px; border: 2px solid #ddd; border-radius: 8px; transition: all 0.3s;\">\n        <img src=\"../../../assets/images/wx.jpg\" alt=\"微信支付\" style=\"width: 150px; height: 50px; display: block;\">\n        <p style=\"margin-top: 10px; font-size: 14px;\">微信支付</p>\n      </div>\n\n      <div @click=\"selectPayMethod('alipay')\"\n           :class=\"['pay-method', { 'selected': selectedPayMethod === 'alipay' }]\"\n           style=\"cursor: pointer; padding: 10px; border: 2px solid #ddd; border-radius: 8px; transition: all 0.3s;\">\n        <img src=\"../../../assets/images/zfb.jpg\" alt=\"支付宝\" style=\"width: 150px; height: 50px; display: block;\">\n        <p style=\"margin-top: 10px; font-size: 14px;\">支付宝</p>\n      </div>\n    </div>\n  </div>\n\n  <div slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"payDialogVisible = false\">取 消</el-button>\n    <el-button type=\"primary\" @click=\"confirmPayment\" :disabled=\"!selectedPayMethod\" :loading=\"payLoading\">确认支付</el-button>\n  </div>\n</el-dialog>\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'waterelectricityfee',\n  components: {\n    \n  },  \n    data() {\n      return {\n               filters: {\n          //列表查询参数\n          dbid: '',\n          doro: '',\n          ftype: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        dormbuildingList: [], //宿舍楼\ndormitoryList: [], //宿舍编号\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n\n        // 支付相关数据\n        payDialogVisible: false, //支付弹窗显示状态\n        currentPayAmount: 0, //当前支付金额\n        currentPayRecord: null, //当前支付记录\n        selectedPayMethod: '', //选择的支付方式\n        payLoading: false, //支付按钮加载状态\n\n      };\n    },\n    created() {\n      this.getDatas();\n      this.getdormbuildingList();\n      this.getdormitoryList();\n    },\n\n \n    methods: {    \n\n              \n       // 删除水电费\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/waterelectricityfee/del?id=\" + row.id;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n      getDatas() {   \n           var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息   \n          let para = {\n               \n  condition:\" and  a.doro in(select doro from student where sno = \"+user.sno+\")\",\n   ftype:this.filters.ftype,\n\n          };\n          this.listLoading = true;\n          let url = base + \"/waterelectricityfee/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;        \n          request.post(url, para).then((res) => {   \n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },    \n                 //查询\n        query() {\n          this.getDatas();\n        },  \n            \n    getdormbuildingList() {\n      let para = {};\n      this.listLoading = true;\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n      request.post(url, para).then((res) => {\n        this.dormbuildingList = res.resdata;\n      });\n    },\n    \n    getdormitoryList() {\n      let para = {};\n      this.listLoading = true;\n      let url = base + \"/dormitory/list?currentPage=1&pageSize=1000\";\n      request.post(url, para).then((res) => {\n        this.dormitoryList = res.resdata;\n      });\n    },\n   \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/WaterelectricityfeeDetail\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/WaterelectricityfeeEdit\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n\n        // 缴纳水电费\n        handlePay(index, row) {\n          this.currentPayRecord = row;\n          this.currentPayAmount = row.amount;\n          this.selectedPayMethod = '';\n          this.payDialogVisible = true;\n        },\n\n        // 选择支付方式\n        selectPayMethod(method) {\n          this.selectedPayMethod = method;\n        },\n\n        // 确认支付\n        confirmPayment() {\n          this.payLoading = true;\n\n          // 模拟支付处理时间\n          setTimeout(() => {\n            let url = base + \"/waterelectricityfee/pay\";\n            let para = {\n              id: this.currentPayRecord.id,\n              payMethod: this.selectedPayMethod\n            };\n\n            request.post(url, para).then((res) => {\n              this.payLoading = false;\n              if (res.code == 200) {\n                this.$message({\n                  message: \"缴纳成功！\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.payDialogVisible = false;\n                this.getDatas(); // 刷新列表\n              } else {\n                this.$message({\n                  message: res.msg || \"缴纳失败，请重试\",\n                  type: \"error\",\n                  offset: 320,\n                });\n              }\n            }).catch(() => {\n              this.payLoading = false;\n              this.$message({\n                message: \"缴纳失败，请重试\",\n                type: \"error\",\n                offset: 320,\n              });\n            });\n          }, 1500); // 模拟1.5秒支付处理时间\n        },\n      },\n}\n\n</script>\n<style scoped>\n.pay-method {\n  transition: all 0.3s ease;\n}\n\n.pay-method:hover {\n  border-color: #409EFF !important;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\n}\n\n.pay-method.selected {\n  border-color: #409EFF !important;\n  background-color: #f0f9ff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\n}\n\n.pay-method img {\n  object-fit: contain;\n}\n</style>\n \n\n"], "mappings": ";AAsEA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACEC,OAAO,EAAE;QACd;QACAC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE;MACT,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;;MACDC,OAAO,EAAE,KAAK;MACdC,gBAAgB,EAAE,EAAE;MAAE;MAC9BC,aAAa,EAAE,EAAE;MAAE;;MAEXC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE;MAAE;;MAEd;MACAC,gBAAgB,EAAE,KAAK;MAAE;MACzBC,gBAAgB,EAAE,CAAC;MAAE;MACrBC,gBAAgB,EAAE,IAAI;MAAE;MACxBC,iBAAiB,EAAE,EAAE;MAAE;MACvBC,UAAU,EAAE,KAAK,CAAE;IAErB,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB,CAAC;EAGDC,OAAO,EAAE;IAGN;IACCC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACpB,WAAU,GAAI,IAAI;QACvB,IAAIqB,GAAE,GAAIpC,IAAG,GAAI,8BAA6B,GAAI8B,GAAG,CAACO,EAAE;QACxDtC,OAAO,CAACuC,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACxB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACyB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAClB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAmB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAACrC,IAAI,CAACC,WAAU,GAAIoC,GAAG;MAC3B,IAAI,CAACrB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACFA,QAAQA,CAAA,EAAG;MACN,IAAIsB,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;MACxD,IAAIC,IAAG,GAAI;QAEnBC,SAAS,EAAC,uDAAuD,GAACN,IAAI,CAACO,GAAG,GAAC,GAAG;QAC7E9C,KAAK,EAAC,IAAI,CAACH,OAAO,CAACG;MAEZ,CAAC;MACD,IAAI,CAACQ,WAAU,GAAI,IAAI;MACvB,IAAIqB,GAAE,GAAIpC,IAAG,GAAI,wCAAuC,GAAI,IAAI,CAACQ,IAAI,CAACC,WAAW,GAAE,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MACpHX,OAAO,CAACuC,IAAI,CAACF,GAAG,EAAEe,IAAI,CAAC,CAAChB,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACe,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAAChD,IAAI,CAACG,UAAS,GAAI4B,GAAG,CAACkB,KAAK;QAChC,IAAI,CAACxC,QAAO,GAAIsB,GAAG,CAACe,OAAO;QAC3B,IAAI,CAACvC,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACQ;IACT2C,KAAKA,CAAA,EAAG;MACN,IAAI,CAAClC,QAAQ,CAAC,CAAC;IACjB,CAAC;IAELC,mBAAmBA,CAAA,EAAG;MACpB,IAAI0B,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACpC,WAAU,GAAI,IAAI;MACvB,IAAIqB,GAAE,GAAIpC,IAAG,GAAI,gDAAgD;MACjED,OAAO,CAACuC,IAAI,CAACF,GAAG,EAAEe,IAAI,CAAC,CAAChB,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAI,CAAC1B,gBAAe,GAAI0B,GAAG,CAACe,OAAO;MACrC,CAAC,CAAC;IACJ,CAAC;IAED5B,gBAAgBA,CAAA,EAAG;MACjB,IAAIyB,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACpC,WAAU,GAAI,IAAI;MACvB,IAAIqB,GAAE,GAAIpC,IAAG,GAAI,6CAA6C;MAC9DD,OAAO,CAACuC,IAAI,CAACF,GAAG,EAAEe,IAAI,CAAC,CAAChB,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAI,CAACzB,aAAY,GAAIyB,GAAG,CAACe,OAAO;MAClC,CAAC,CAAC;IACJ,CAAC;IAEG;IACAK,UAAUA,CAAC9B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC8B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,4BAA4B;QACjCJ,KAAK,EAAE;UACJrB,EAAE,EAAEP,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACA0B,UAAUA,CAAClC,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC8B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,0BAA0B;QAC/BJ,KAAK,EAAE;UACJrB,EAAE,EAAEP,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACA2B,SAASA,CAACnC,KAAK,EAAEC,GAAG,EAAE;MACpB,IAAI,CAACV,gBAAe,GAAIU,GAAG;MAC3B,IAAI,CAACX,gBAAe,GAAIW,GAAG,CAACmC,MAAM;MAClC,IAAI,CAAC5C,iBAAgB,GAAI,EAAE;MAC3B,IAAI,CAACH,gBAAe,GAAI,IAAI;IAC9B,CAAC;IAED;IACAgD,eAAeA,CAACC,MAAM,EAAE;MACtB,IAAI,CAAC9C,iBAAgB,GAAI8C,MAAM;IACjC,CAAC;IAED;IACAC,cAAcA,CAAA,EAAG;MACf,IAAI,CAAC9C,UAAS,GAAI,IAAI;;MAEtB;MACA+C,UAAU,CAAC,MAAM;QACf,IAAIjC,GAAE,GAAIpC,IAAG,GAAI,0BAA0B;QAC3C,IAAImD,IAAG,GAAI;UACTd,EAAE,EAAE,IAAI,CAACjB,gBAAgB,CAACiB,EAAE;UAC5BiC,SAAS,EAAE,IAAI,CAACjD;QAClB,CAAC;QAEDtB,OAAO,CAACuC,IAAI,CAACF,GAAG,EAAEe,IAAI,CAAC,CAAChB,IAAI,CAAEI,GAAG,IAAK;UACpC,IAAI,CAACjB,UAAS,GAAI,KAAK;UACvB,IAAIiB,GAAG,CAACgC,IAAG,IAAK,GAAG,EAAE;YACnB,IAAI,CAAC/B,QAAQ,CAAC;cACZC,OAAO,EAAE,OAAO;cAChBP,IAAI,EAAE,SAAS;cACfQ,MAAM,EAAE;YACV,CAAC,CAAC;YACF,IAAI,CAACxB,gBAAe,GAAI,KAAK;YAC7B,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAE;UACnB,OAAO;YACL,IAAI,CAACgB,QAAQ,CAAC;cACZC,OAAO,EAAEF,GAAG,CAACiC,GAAE,IAAK,UAAU;cAC9BtC,IAAI,EAAE,OAAO;cACbQ,MAAM,EAAE;YACV,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM;UACb,IAAI,CAACrB,UAAS,GAAI,KAAK;UACvB,IAAI,CAACkB,QAAQ,CAAC;YACZC,OAAO,EAAE,UAAU;YACnBP,IAAI,EAAE,OAAO;YACbQ,MAAM,EAAE;UACV,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC,EAAE;IACZ;EACF;AACN"}]}