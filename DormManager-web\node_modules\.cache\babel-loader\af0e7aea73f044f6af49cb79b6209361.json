{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeManage2.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeManage2.vue", "mtime": 1749048506769}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "dbid", "doro", "ftype", "page", "currentPage", "pageSize", "totalCount", "isClear", "dormbuildingList", "dormitoryList", "listLoading", "btnLoading", "datalist", "created", "getDatas", "getdormbuildingList", "getdormitoryList", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "id", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "user", "JSON", "parse", "sessionStorage", "getItem", "para", "condition", "sno", "resdata", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "handleEdit"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeManage2.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      <el-col  :span=\"24\"  style=\"padding-bottom: 0px; margin-left: 10px\">\n<el-form :inline=\"true\" :model=\"filters\" >\n\n<el-form-item label=\"费用类型\" prop=\"ftype\">\n<el-select v-model=\"filters.ftype\" placeholder=\"请选择\"  size=\"small\">\n<el-option label=\"全部\" value=\"\"></el-option>\n<el-option label=\"水费\" value=\"水费\"></el-option>\n<el-option label=\"电费\" value=\"电费\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n</el-form-item>\n </el-form>\n</el-col>\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"dbname\" label=\"宿舍楼\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"doro\" label=\"宿舍编号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"ftype\" label=\"费用类型\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"title\" label=\"费用标题\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"amount\" label=\"费用金额\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"addtime\" label=\"添加时间\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"status\" label=\"缴纳状态\"  align=\"center\"></el-table-column>\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" \n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" \n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'waterelectricityfee',\n  components: {\n    \n  },  \n    data() {\n      return {\n               filters: {\n          //列表查询参数\n          dbid: '',\n          doro: '',\n          ftype: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        dormbuildingList: [], //宿舍楼\ndormitoryList: [], //宿舍编号\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据  \n    \n      };\n    },\n    created() {\n      this.getDatas();\n      this.getdormbuildingList();\n      this.getdormitoryList();\n    },\n\n \n    methods: {    \n\n              \n       // 删除水电费\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/waterelectricityfee/del?id=\" + row.id;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n      getDatas() {   \n           var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息   \n          let para = {\n               \n  condition:\" and  a.doro in(select doro from student where sno = \"+user.sno+\")\",\n   ftype:this.filters.ftype,\n\n          };\n          this.listLoading = true;\n          let url = base + \"/waterelectricityfee/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;        \n          request.post(url, para).then((res) => {   \n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },    \n                 //查询\n        query() {\n          this.getDatas();\n        },  \n            \n    getdormbuildingList() {\n      let para = {};\n      this.listLoading = true;\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n      request.post(url, para).then((res) => {\n        this.dormbuildingList = res.resdata;\n      });\n    },\n    \n    getdormitoryList() {\n      let para = {};\n      this.listLoading = true;\n      let url = base + \"/dormitory/list?currentPage=1&pageSize=1000\";\n      request.post(url, para).then((res) => {\n        this.dormitoryList = res.resdata;\n      });\n    },\n   \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/WaterelectricityfeeDetail\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/WaterelectricityfeeEdit\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": ";AAwCA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACEC,OAAO,EAAE;QACd;QACAC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE;MACT,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;;MACDC,OAAO,EAAE,KAAK;MACdC,gBAAgB,EAAE,EAAE;MAAE;MAC9BC,aAAa,EAAE,EAAE;MAAE;;MAEXC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE,CAAE;IAEhB,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB,CAAC;EAGDC,OAAO,EAAE;IAGN;IACCC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACf,WAAU,GAAI,IAAI;QACvB,IAAIgB,GAAE,GAAI/B,IAAG,GAAI,8BAA6B,GAAIyB,GAAG,CAACO,EAAE;QACxDjC,OAAO,CAACkC,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACnB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACoB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAClB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAmB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAAChC,IAAI,CAACC,WAAU,GAAI+B,GAAG;MAC3B,IAAI,CAACrB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACFA,QAAQA,CAAA,EAAG;MACN,IAAIsB,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;MACxD,IAAIC,IAAG,GAAI;QAEnBC,SAAS,EAAC,uDAAuD,GAACN,IAAI,CAACO,GAAG,GAAC,GAAG;QAC7EzC,KAAK,EAAC,IAAI,CAACH,OAAO,CAACG;MAEZ,CAAC;MACD,IAAI,CAACQ,WAAU,GAAI,IAAI;MACvB,IAAIgB,GAAE,GAAI/B,IAAG,GAAI,wCAAuC,GAAI,IAAI,CAACQ,IAAI,CAACC,WAAW,GAAE,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MACpHX,OAAO,CAACkC,IAAI,CAACF,GAAG,EAAEe,IAAI,CAAC,CAAChB,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACe,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAAC3C,IAAI,CAACG,UAAS,GAAIuB,GAAG,CAACkB,KAAK;QAChC,IAAI,CAACnC,QAAO,GAAIiB,GAAG,CAACe,OAAO;QAC3B,IAAI,CAAClC,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACQ;IACTsC,KAAKA,CAAA,EAAG;MACN,IAAI,CAAClC,QAAQ,CAAC,CAAC;IACjB,CAAC;IAELC,mBAAmBA,CAAA,EAAG;MACpB,IAAI0B,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAAC/B,WAAU,GAAI,IAAI;MACvB,IAAIgB,GAAE,GAAI/B,IAAG,GAAI,gDAAgD;MACjED,OAAO,CAACkC,IAAI,CAACF,GAAG,EAAEe,IAAI,CAAC,CAAChB,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAI,CAACrB,gBAAe,GAAIqB,GAAG,CAACe,OAAO;MACrC,CAAC,CAAC;IACJ,CAAC;IAED5B,gBAAgBA,CAAA,EAAG;MACjB,IAAIyB,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAAC/B,WAAU,GAAI,IAAI;MACvB,IAAIgB,GAAE,GAAI/B,IAAG,GAAI,6CAA6C;MAC9DD,OAAO,CAACkC,IAAI,CAACF,GAAG,EAAEe,IAAI,CAAC,CAAChB,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAI,CAACpB,aAAY,GAAIoB,GAAG,CAACe,OAAO;MAClC,CAAC,CAAC;IACJ,CAAC;IAEG;IACAK,UAAUA,CAAC9B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC8B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,4BAA4B;QACjCJ,KAAK,EAAE;UACJrB,EAAE,EAAEP,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACA0B,UAAUA,CAAClC,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC8B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,0BAA0B;QAC/BJ,KAAK,EAAE;UACJrB,EAAE,EAAEP,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ;EACF;AACN"}]}