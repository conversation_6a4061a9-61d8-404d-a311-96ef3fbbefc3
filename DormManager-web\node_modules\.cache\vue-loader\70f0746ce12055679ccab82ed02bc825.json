{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeManage2.vue?vue&type=style&index=0&id=535cc11e&scoped=true&lang=css", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeManage2.vue", "mtime": 1749049139666}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749043012394}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749043013744}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749043012910}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5wYXktbWV0aG9kIHsKICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwp9CgoucGF5LW1ldGhvZDpob3ZlciB7CiAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGICFpbXBvcnRhbnQ7CiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjMpOwp9CgoucGF5LW1ldGhvZC5zZWxlY3RlZCB7CiAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGICFpbXBvcnRhbnQ7CiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjlmZjsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuMyk7Cn0KCi5wYXktbWV0aG9kIGltZyB7CiAgb2JqZWN0LWZpdDogY29udGFpbjsKfQo="}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\waterelectricityfee\\WaterelectricityfeeManage2.vue"], "names": [], "mappings": ";AA6QA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/waterelectricityfee/WaterelectricityfeeManage2.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      <el-col  :span=\"24\"  style=\"padding-bottom: 0px; margin-left: 10px\">\n<el-form :inline=\"true\" :model=\"filters\" >\n\n<el-form-item label=\"费用类型\" prop=\"ftype\">\n<el-select v-model=\"filters.ftype\" placeholder=\"请选择\"  size=\"small\">\n<el-option label=\"全部\" value=\"\"></el-option>\n<el-option label=\"水费\" value=\"水费\"></el-option>\n<el-option label=\"电费\" value=\"电费\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n</el-form-item>\n </el-form>\n</el-col>\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"dbname\" label=\"宿舍楼\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"doro\" label=\"宿舍编号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"ftype\" label=\"费用类型\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"title\" label=\"费用标题\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"amount\" label=\"费用金额\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"addtime\" label=\"添加时间\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"status\" label=\"缴纳状态\"  align=\"center\"></el-table-column>\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button v-if=\"scope.row.status === '未缴纳'\" type=\"success\" size=\"mini\" @click=\"handlePay(scope.$index, scope.row)\" icon=\"el-icon-money\" style=\" padding: 3px 6px 3px 6px; margin-left: 5px;\">缴纳</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\"\n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n<!-- 支付弹窗 -->\n<el-dialog title=\"水电费缴纳\" v-model=\"payDialogVisible\" width=\"400px\" center>\n  <div style=\"text-align: center;\">\n    <h3>缴纳金额：<span style=\"color: #f56c6c; font-size: 24px;\">￥{{ currentPayAmount }}</span></h3>\n    <p style=\"margin: 20px 0;\">请选择支付方式：</p>\n\n    <div style=\"display: flex; justify-content: space-around; margin: 30px 0;\">\n      <div @click=\"selectPayMethod('wechat')\"\n           :class=\"['pay-method', { 'selected': selectedPayMethod === 'wechat' }]\"\n           style=\"cursor: pointer; padding: 10px; border: 2px solid #ddd; border-radius: 8px; transition: all 0.3s;\">\n        <img src=\"../../../assets/images/wx.jpg\" alt=\"微信支付\" style=\"width: 150px; height: 50px; display: block;\">\n        <p style=\"margin-top: 10px; font-size: 14px;\">微信支付</p>\n      </div>\n\n      <div @click=\"selectPayMethod('alipay')\"\n           :class=\"['pay-method', { 'selected': selectedPayMethod === 'alipay' }]\"\n           style=\"cursor: pointer; padding: 10px; border: 2px solid #ddd; border-radius: 8px; transition: all 0.3s;\">\n        <img src=\"../../../assets/images/zfb.jpg\" alt=\"支付宝\" style=\"width: 150px; height: 50px; display: block;\">\n        <p style=\"margin-top: 10px; font-size: 14px;\">支付宝</p>\n      </div>\n    </div>\n  </div>\n\n  <div slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"payDialogVisible = false\">取 消</el-button>\n    <el-button type=\"primary\" @click=\"confirmPayment\" :disabled=\"!selectedPayMethod\" :loading=\"payLoading\">确认支付</el-button>\n  </div>\n</el-dialog>\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'waterelectricityfee',\n  components: {\n    \n  },  \n    data() {\n      return {\n               filters: {\n          //列表查询参数\n          dbid: '',\n          doro: '',\n          ftype: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        dormbuildingList: [], //宿舍楼\ndormitoryList: [], //宿舍编号\n\n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n\n        // 支付相关数据\n        payDialogVisible: false, //支付弹窗显示状态\n        currentPayAmount: 0, //当前支付金额\n        currentPayRecord: null, //当前支付记录\n        selectedPayMethod: '', //选择的支付方式\n        payLoading: false, //支付按钮加载状态\n\n      };\n    },\n    created() {\n      this.getDatas();\n      this.getdormbuildingList();\n      this.getdormitoryList();\n    },\n\n \n    methods: {    \n\n              \n       // 删除水电费\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/waterelectricityfee/del?id=\" + row.id;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n      getDatas() {   \n           var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息   \n          let para = {\n               \n  condition:\" and  a.doro in(select doro from student where sno = \"+user.sno+\")\",\n   ftype:this.filters.ftype,\n\n          };\n          this.listLoading = true;\n          let url = base + \"/waterelectricityfee/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;        \n          request.post(url, para).then((res) => {   \n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },    \n                 //查询\n        query() {\n          this.getDatas();\n        },  \n            \n    getdormbuildingList() {\n      let para = {};\n      this.listLoading = true;\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n      request.post(url, para).then((res) => {\n        this.dormbuildingList = res.resdata;\n      });\n    },\n    \n    getdormitoryList() {\n      let para = {};\n      this.listLoading = true;\n      let url = base + \"/dormitory/list?currentPage=1&pageSize=1000\";\n      request.post(url, para).then((res) => {\n        this.dormitoryList = res.resdata;\n      });\n    },\n   \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/WaterelectricityfeeDetail\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/WaterelectricityfeeEdit\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n\n        // 缴纳水电费\n        handlePay(index, row) {\n          this.currentPayRecord = row;\n          this.currentPayAmount = row.amount;\n          this.selectedPayMethod = '';\n          this.payDialogVisible = true;\n        },\n\n        // 选择支付方式\n        selectPayMethod(method) {\n          this.selectedPayMethod = method;\n        },\n\n        // 确认支付\n        confirmPayment() {\n          this.payLoading = true;\n\n          // 模拟支付处理时间\n          setTimeout(() => {\n            let url = base + \"/waterelectricityfee/pay\";\n            let para = {\n              id: this.currentPayRecord.id,\n              payMethod: this.selectedPayMethod\n            };\n\n            request.post(url, para).then((res) => {\n              this.payLoading = false;\n              if (res.code == 200) {\n                this.$message({\n                  message: \"缴纳成功！\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.payDialogVisible = false;\n                this.getDatas(); // 刷新列表\n              } else {\n                this.$message({\n                  message: res.msg || \"缴纳失败，请重试\",\n                  type: \"error\",\n                  offset: 320,\n                });\n              }\n            }).catch(() => {\n              this.payLoading = false;\n              this.$message({\n                message: \"缴纳失败，请重试\",\n                type: \"error\",\n                offset: 320,\n              });\n            });\n          }, 1500); // 模拟1.5秒支付处理时间\n        },\n      },\n}\n\n</script>\n<style scoped>\n.pay-method {\n  transition: all 0.3s ease;\n}\n\n.pay-method:hover {\n  border-color: #409EFF !important;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\n}\n\n.pay-method.selected {\n  border-color: #409EFF !important;\n  background-color: #f0f9ff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\n}\n\n.pay-method img {\n  object-fit: contain;\n}\n</style>\n \n\n"]}]}