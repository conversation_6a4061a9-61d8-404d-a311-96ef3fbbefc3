﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">

<el-form-item label="返校日期" prop="returndate">
<el-date-picker v-model="formData.returndate" type="date" placeholder="返校日期"  style="width:50%;" ></el-date-picker>
</el-form-item>
<el-form-item label="返校原因" prop="reason">
<el-input v-model="formData.reason" placeholder="返校原因"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="交通方式" prop="transport">
<el-input v-model="formData.transport" placeholder="交通方式"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="具体说明" prop="note">
<WangEditor  ref="wangEditorRef" v-model="formData.note" :config="editorConfig"   :isClear="isClear" @change="editorChange"></WangEditor>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
import WangEditor from "../../../components/WangEditor";
export default {
  name: 'RegisterinfoAdd',
  components: {
    WangEditor,
  },  
    data() {
      return {   
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },
],          returndate: [{ required: true, message: '请输入返校日期', trigger: 'blur' },
],          reason: [{ required: true, message: '请输入返校原因', trigger: 'blur' },
],          transport: [{ required: true, message: '请输入交通方式', trigger: 'blur' },
],        },

      };
    },
    mounted() {
    
    },

 
    methods: {    
   // 添加
    save() {       
         this.$refs["formDataRef"].validate((valid) => { //验证表单
           if (valid) {
             let url = base + "/registerinfo/add";
             this.btnLoading = true;
             var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息
             this.formData.sno = user.sno;
             request.post(url, this.formData).then((res) => { //发送请求         
               if (res.code == 200) {
                 this.$message({
                   message: "操作成功",
                   type: "success",
                   offset: 320,
                 });              
                this.$router.push({
                path: "/RegisterinfoManage",
                });
               } else {
                 this.$message({
                   message: res.msg,
                   type: "error",
                   offset: 320,
                 });
               }
               this.btnLoading=false;
             });
           }        
           
         });
    },
    
       // 返回
        goBack() {
          this.$router.push({
            path: "/RegisterinfoManage",
          });
        },       
              
          
           
            // 富文本编辑器
    editorChange(val) {
      this.formData.note = val;
    },
   
      },
}

</script>
<style scoped>
</style>
 

