package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/waterelectricityfee")
public class WaterelectricityfeeController{
	
	@Resource
	private WaterelectricityfeeService waterelectricityfeeService;
	
	//水电费列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Waterelectricityfee>> list(@RequestBody Waterelectricityfee waterelectricityfee, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = waterelectricityfeeService.getCount(waterelectricityfee);
		//获取当前页记录
		List<Waterelectricityfee> waterelectricityfeeList = waterelectricityfeeService.queryWaterelectricityfeeList(waterelectricityfee, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(waterelectricityfeeList, counts, page_count);
	}
        
	//添加水电费
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Waterelectricityfee waterelectricityfee, HttpServletRequest req) throws Exception {
		try {
			waterelectricityfeeService.insertWaterelectricityfee(waterelectricityfee); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除水电费
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			waterelectricityfeeService.deleteWaterelectricityfee(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改水电费
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Waterelectricityfee waterelectricityfee, HttpServletRequest req) throws Exception {
		try {
			waterelectricityfeeService.updateWaterelectricityfee(waterelectricityfee); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回水电费详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Waterelectricityfee waterelectricityfee=waterelectricityfeeService.queryWaterelectricityfeeById(id); //根据ID查询
			return Response.success(waterelectricityfee);
			} catch (Exception e) {
			return Response.error();
		}

	}

	//水电费缴纳
	@ResponseBody
	@PostMapping(value = "/pay")
	@CrossOrigin
	public Response pay(@RequestBody Map<String, Object> params, HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(params.get("id").toString());
			String payMethod = params.get("payMethod").toString();

			// 获取水电费记录
			Waterelectricityfee waterelectricityfee = waterelectricityfeeService.queryWaterelectricityfeeById(id);
			if (waterelectricityfee == null) {
				return Response.error(404, "水电费记录不存在");
			}

			// 检查是否已经缴纳
			if ("已缴纳".equals(waterelectricityfee.getStatus())) {
				return Response.error(400, "该费用已经缴纳，请勿重复缴纳");
			}

			// 更新缴纳状态
			waterelectricityfee.setStatus("已缴纳");
			waterelectricityfeeService.updateWaterelectricityfee(waterelectricityfee);

			return Response.success();
		} catch (Exception e) {
			e.printStackTrace();
			return Response.error(500, "缴纳失败，请重试");
		}
	}

}

