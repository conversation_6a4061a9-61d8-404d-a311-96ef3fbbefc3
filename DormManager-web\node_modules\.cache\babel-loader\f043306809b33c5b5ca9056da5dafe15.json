{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\repairorders\\RepairordersAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\repairorders\\RepairordersAdd.vue", "mtime": 1749048547783}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "WangEditor", "name", "components", "data", "uploadVisible", "btnLoading", "formData", "add<PERSON><PERSON>", "typeid", "required", "message", "trigger", "tsubject", "description", "mounted", "getrepairtypeList", "methods", "save", "$refs", "validate", "valid", "url", "user", "JSON", "parse", "sessionStorage", "getItem", "sno", "progress", "doro", "post", "then", "res", "code", "$message", "type", "offset", "$router", "push", "path", "msg", "goBack", "para", "listLoading", "repairtypeList", "resdata", "<PERSON><PERSON><PERSON><PERSON>", "val"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\repairorders\\RepairordersAdd.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n\r\n<el-form-item label=\"报修类型\" prop=\"typeid\">\r\n<el-select v-model=\"formData.typeid\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option v-for=\"item in repairtypeList\" :key=\"item.typeid\" :label=\"item.typename\" :value=\"item.typeid\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"报修主题\" prop=\"tsubject\">\r\n<el-input v-model=\"formData.tsubject\" placeholder=\"报修主题\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"具体描述\" prop=\"description\">\r\n<WangEditor  ref=\"wangEditorRef\" v-model=\"formData.description\" :config=\"editorConfig\"   :isClear=\"isClear\" @change=\"editorChange\"></WangEditor>\r\n</el-form-item>\r\n\r\n\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\nimport WangEditor from \"../../../components/WangEditor\";\r\nexport default {\r\n  name: 'RepairordersAdd',\r\n  components: {\r\n    WangEditor,\r\n  },  \r\n    data() {\r\n      return {   \r\n        uploadVisible: false, \r\n        btnLoading: false, //保存按钮加载状态     \r\n        formData: {}, //表单数据           \r\n        addrules: {\r\n          typeid: [{ required: true, message: '请选择报修类型', trigger: 'change' },\r\n],          tsubject: [{ required: true, message: '请输入报修主题', trigger: 'blur' },\r\n],          description: [{ required: true, message: '请输入具体描述', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n    mounted() {\r\n    \r\n      this.getrepairtypeList();\r\n    },\r\n\r\n \r\n    methods: {    \r\n   // 添加\r\n    save() {       \r\n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n           if (valid) {\r\n             let url = base + \"/repairorders/add\";\r\n             this.btnLoading = true;\r\n             var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息\r\n             this.formData.sno = user.sno;\r\n             this.formData.progress = \"待处理\";\r\n             this.formData.doro = user.doro;\r\n             request.post(url, this.formData).then((res) => { //发送请求         \r\n               if (res.code == 200) {\r\n                 this.$message({\r\n                   message: \"提交成功，请等待处理\",\r\n                   type: \"success\",\r\n                   offset: 320,\r\n                 });              \r\n                this.$router.push({\r\n                path: \"/RepairordersManage\",\r\n                });\r\n               } else {\r\n                 this.$message({\r\n                   message: res.msg,\r\n                   type: \"error\",\r\n                   offset: 320,\r\n                 });\r\n               }\r\n               this.btnLoading=false;\r\n             });\r\n           }        \r\n           \r\n         });\r\n    },\r\n    \r\n       // 返回\r\n        goBack() {\r\n          this.$router.push({\r\n            path: \"/RepairordersManage\",\r\n          });\r\n        },       \r\n              \r\n            \r\n    getrepairtypeList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/repairtype/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.repairtypeList = res.resdata;\r\n      });\r\n    },\r\n  \r\n           \r\n            // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.description = val;\r\n    },\r\n   \r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";AA4BA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAOC,UAAS,MAAO,gCAAgC;AACvD,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE;IACVF;EACF,CAAC;EACCG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,QAAQ,EAAE;QACRC,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAS,CAAC,CAC3E;QAAWC,QAAQ,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC7E;QAAWE,WAAW,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MACvE;IAEJ,CAAC;EACH,CAAC;EACDG,OAAOA,CAAA,EAAG;IAER,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B,CAAC;EAGDC,OAAO,EAAE;IACV;IACCC,IAAIA,CAAA,EAAG;MACF,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIC,GAAE,GAAItB,IAAG,GAAI,mBAAmB;UACpC,IAAI,CAACM,UAAS,GAAI,IAAI;UACtB,IAAIiB,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;UACvD,IAAI,CAACpB,QAAQ,CAACqB,GAAE,GAAIL,IAAI,CAACK,GAAG;UAC5B,IAAI,CAACrB,QAAQ,CAACsB,QAAO,GAAI,KAAK;UAC9B,IAAI,CAACtB,QAAQ,CAACuB,IAAG,GAAIP,IAAI,CAACO,IAAI;UAC9B/B,OAAO,CAACgC,IAAI,CAACT,GAAG,EAAE,IAAI,CAACf,QAAQ,CAAC,CAACyB,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZxB,OAAO,EAAE,YAAY;gBACrByB,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACH,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAClBC,IAAI,EAAE;cACN,CAAC,CAAC;YACH,OAAO;cACL,IAAI,CAACL,QAAQ,CAAC;gBACZxB,OAAO,EAAEsB,GAAG,CAACQ,GAAG;gBAChBL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAAC/B,UAAU,GAAC,KAAK;UACvB,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACP,CAAC;IAEE;IACCoC,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAGLxB,iBAAiBA,CAAA,EAAG;MAClB,IAAI2B,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAItB,GAAE,GAAItB,IAAG,GAAI,8CAA8C;MAC/DD,OAAO,CAACgC,IAAI,CAACT,GAAG,EAAEqB,IAAI,CAAC,CAACX,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACY,cAAa,GAAIZ,GAAG,CAACa,OAAO;MACnC,CAAC,CAAC;IACJ,CAAC;IAGO;IACRC,YAAYA,CAACC,GAAG,EAAE;MAChB,IAAI,CAACzC,QAAQ,CAACO,WAAU,GAAIkC,GAAG;IACjC;EAEE;AACN"}]}