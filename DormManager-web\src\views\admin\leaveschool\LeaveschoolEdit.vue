﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="离校登记id">
{{formData.lid}}</el-form-item>
<el-form-item label="学号">
{{formData.sno}}</el-form-item>
<el-form-item label="离校日期">
{{formData.ldate}}</el-form-item>
<el-form-item label="离校原因">
{{formData.reason}}</el-form-item>
<el-form-item label="目的地">
{{formData.destination}}</el-form-item>
<el-form-item label="具体说明">
{{formData.note}}</el-form-item>
<el-form-item label="提交时间">
{{formData.submittime}}</el-form-item>
<el-form-item label="审核状态" prop="lflag">
<el-radio-group v-model="formData.lflag">
<el-radio label="审核通过">
审核通过
</el-radio>
<el-radio label="审核不通过">
审核不通过
</el-radio>
</el-radio-group>
</el-form-item>
<el-form-item label="审核回复" prop="reply">
<el-input type="textarea" :rows="5" v-model="formData.reply" placeholder="审核回复"  size="small"></el-input>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'LeaveschoolEdit',
  components: {
    
  },  
    data() {
      return {   
        id: '',
        isClear: false,
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {
          lflag: '审核通过',
        }, //表单数据           
        addrules: {
          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },
],          ldate: [{ required: true, message: '请输入离校日期', trigger: 'blur' },
],          reason: [{ required: true, message: '请输入离校原因', trigger: 'blur' },
],          destination: [{ required: true, message: '请输入目的地', trigger: 'blur' },
],          note: [{ required: true, message: '请输入具体说明', trigger: 'blur' },
],          lflag: [{ required: true, message: '请输入审核状态', trigger: 'blur' },
],          reply: [{ required: true, message: '请输入审核回复', trigger: 'blur' },
],        },

      };
    },
    created() {
    this.id = this.$route.query.id;
      this.getDatas();
    },

 
    methods: {    

//获取列表数据
        getDatas() {
          let para = {
          };
          this.listLoading = true;
          let url = base + "/leaveschool/get?id=" + this.id;
          request.post(url, para).then((res) => {
            this.formData = JSON.parse(JSON.stringify(res.resdata));
            this.listLoading = false;
            
            
          });
        },
    
        // 添加
        save() {
          this.$refs["formDataRef"].validate((valid) => { //验证表单
            if (valid) {
              let url = base + "/leaveschool/update";
              this.btnLoading = true;
              
              request.post(url, this.formData).then((res) => { //发送请求         
                if (res.code == 200) {
                  this.$message({
                    message: "操作成功",
                    type: "success",
                    offset: 320,
                  });
                  this.$router.push({
                    path: "/LeaveschoolManage2",
                  });
                } else {
                  this.$message({
                    message:res.msg,
                    type: "error",
                    offset: 320,
                  });
                }
                this.btnLoading = false;
              });
            }
    
          });
        },
        
       // 返回
        goBack() {
          this.$router.push({
            path: "/LeaveschoolManage2",
          });
        },       
              
          
           
           
      },
}

</script>
<style scoped>
</style>
 

