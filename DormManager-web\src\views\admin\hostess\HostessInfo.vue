<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="账号" prop="hno">
<el-input v-model="formData.hno" placeholder="账号"  style="width:50%;" disabled ></el-input>
</el-form-item>
<el-form-item label="姓名" prop="honame">
<el-input v-model="formData.honame" placeholder="姓名"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="年龄" prop="age">
<el-input v-model="formData.age" placeholder="年龄"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="手机号码" prop="phone">
<el-input v-model="formData.phone" placeholder="手机号码"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="联系地址" prop="address">
<el-input v-model="formData.address" placeholder="联系地址"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="负责宿舍楼" prop="dbid">
<el-select v-model="formData.dbid" placeholder="请选择"  size="small" disabled>
<el-option v-for="item in dormbuildingList" :key="item.dbid" :label="item.dbname" :value="item.dbid" ></el-option>
</el-select>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'HostessInfo',
  components: {
    
  },  
    data() {
      return {   
        id: '',
        isClear: false,
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          honame: [{ required: true, message: '请输入姓名', trigger: 'blur' },
],          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },
        { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },
],          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },
],          dbid: [{ required: true, message: '请选择负责宿舍楼', trigger: 'onchange' }],
        },

      };
    },
     created() {
        var user = JSON.parse(sessionStorage.getItem("user"));
        this.id = user.hno;
        this.getDatas();
      }, 
    methods: {    

//获取列表数据
        getDatas() {
          let para = {
          };
          this.listLoading = true;
          let url = base + "/hostess/get?id=" + this.id;
          request.post(url, para).then((res) => {
            this.formData = JSON.parse(JSON.stringify(res.resdata));
            this.listLoading = false;
            
                    this.dbid = this.formData.dbid;
        this.formData.dbid = this.formData.dbname;

          });
        },
    
        // 添加
        save() {
          this.$refs["formDataRef"].validate((valid) => { //验证表单
            if (valid) {
              let url = base + "/hostess/update";
              this.btnLoading = true;
                        this.formData.dbid = this.formData.dbname==this.formData.dbid?this.dbid:this.formData.dbid;

              request.post(url, this.formData).then((res) => { //发送请求         
                if (res.code == 200) {
                  this.$message({
                    message: "操作成功",
                    type: "success",
                    offset: 320,
                  });                 
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                    offset: 320,
                  });
                }
                this.btnLoading = false;
              });
            }
    
          });
        },
       
              
            
    getdormbuildingList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/dormbuilding/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.dormbuildingList = res.resdata;
      });
    },
  
           
           
      },
}

</script>
<style scoped>
</style>
 

