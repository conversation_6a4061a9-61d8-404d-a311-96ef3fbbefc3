﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
      <el-col  :span="24"  style="padding-bottom: 0px; margin-left: 10px">
<el-form :inline="true" :model="filters" >

<el-form-item label="费用类型" prop="ftype">
<el-select v-model="filters.ftype" placeholder="请选择"  size="small">
<el-option label="全部" value=""></el-option>
<el-option label="水费" value="水费"></el-option>
<el-option label="电费" value="电费"></el-option>
</el-select>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="query" icon="el-icon-search">搜索</el-button>
</el-form-item>
 </el-form>
</el-col>

<el-table :data="datalist" border stripe style="width: 100%"  v-loading="listLoading"   highlight-current-row   max-height="600"     size="small">
<el-table-column prop="dbname" label="宿舍楼"  align="center"></el-table-column>
<el-table-column prop="doro" label="宿舍编号"  align="center"></el-table-column>
<el-table-column prop="ftype" label="费用类型"  align="center"></el-table-column>
<el-table-column prop="title" label="费用标题"  align="center"></el-table-column>
<el-table-column prop="amount" label="费用金额"  align="center"></el-table-column>
<el-table-column prop="addtime" label="添加时间"  align="center"></el-table-column>
<el-table-column prop="status" label="缴纳状态"  align="center"></el-table-column>
<el-table-column label="操作" min-width="200" align="center">
<template #default="scope">
<el-button type="primary" size="mini" @click="handleShow(scope.$index, scope.row)" icon="el-icon-zoom-in" style=" padding: 3px 6px 3px 6px;">详情</el-button>
<el-button v-if="scope.row.status === '未缴纳'" type="success" size="mini" @click="handlePay(scope.$index, scope.row)" icon="el-icon-money" style=" padding: 3px 6px 3px 6px; margin-left: 5px;">缴纳</el-button>
</template>
</el-table-column>
</el-table>
<el-pagination  @current-change="handleCurrentChange" :current-page="page.currentPage" :page-size="page.pageSize"
 background layout="total, prev, pager, next, jumper" :total="page.totalCount"
 style="float: right; margin: 10px 20px 0 0"></el-pagination>

<!-- 支付弹窗 -->
<el-dialog title="水电费缴纳" v-model="payDialogVisible" width="400px" center>
  <div style="text-align: center;">
    <h3>缴纳金额：<span style="color: #f56c6c; font-size: 24px;">￥{{ currentPayAmount }}</span></h3>
    <p style="margin: 20px 0;">请选择支付方式：</p>

    <div style="display: flex; justify-content: space-around; margin: 30px 0;">
      <div @click="selectPayMethod('wechat')"
           :class="['pay-method', { 'selected': selectedPayMethod === 'wechat' }]"
           style="cursor: pointer; padding: 10px; border: 2px solid #ddd; border-radius: 8px; transition: all 0.3s;">
        <img src="../../../assets/images/wx.jpg" alt="微信支付" style="width: 150px; height: 50px; display: block;">
        <p style="margin-top: 10px; font-size: 14px;">微信支付</p>
      </div>

      <div @click="selectPayMethod('alipay')"
           :class="['pay-method', { 'selected': selectedPayMethod === 'alipay' }]"
           style="cursor: pointer; padding: 10px; border: 2px solid #ddd; border-radius: 8px; transition: all 0.3s;">
        <img src="../../../assets/images/zfb.jpg" alt="支付宝" style="width: 150px; height: 50px; display: block;">
        <p style="margin-top: 10px; font-size: 14px;">支付宝</p>
      </div>
    </div>
  </div>

  <div slot="footer" class="dialog-footer">
    <el-button @click="payDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="confirmPayment" :disabled="!selectedPayMethod" :loading="payLoading">确认支付</el-button>
  </div>
</el-dialog>

    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
export default {
  name: 'waterelectricityfee',
  components: {
    
  },  
    data() {
      return {
               filters: {
          //列表查询参数
          dbid: '',
          doro: '',
          ftype: '',
        },

        page: {
          currentPage: 1, // 当前页
          pageSize: 10, // 每页显示条目个数
          totalCount: 0, // 总条目数
        },
        isClear: false,      
        dormbuildingList: [], //宿舍楼
dormitoryList: [], //宿舍编号

        listLoading: false, //列表加载状态
        btnLoading: false, //保存按钮加载状态
        datalist: [], //表格数据

        // 支付相关数据
        payDialogVisible: false, //支付弹窗显示状态
        currentPayAmount: 0, //当前支付金额
        currentPayRecord: null, //当前支付记录
        selectedPayMethod: '', //选择的支付方式
        payLoading: false, //支付按钮加载状态

      };
    },
    created() {
      this.getDatas();
      this.getdormbuildingList();
      this.getdormitoryList();
    },

 
    methods: {    

              
       // 删除水电费
        handleDelete(index, row) {
          this.$confirm("确认删除该记录吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.listLoading = true;
              let url = base + "/waterelectricityfee/del?id=" + row.id;
              request.post(url).then((res) => {
                this.listLoading = false;
             
                this.$message({
                  message: "删除成功",
                  type: "success",
                  offset: 320,
                });
                this.getDatas();
              });
            })
            .catch(() => { });
        },
                
        // 分页
        handleCurrentChange(val) {
          this.page.currentPage = val;
          this.getDatas();
        },     
     
        //获取列表数据
      getDatas() {   
           var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息   
          let para = {
               
  condition:" and  a.doro in(select doro from student where sno = "+user.sno+")",
   ftype:this.filters.ftype,

          };
          this.listLoading = true;
          let url = base + "/waterelectricityfee/list?currentPage=" + this.page.currentPage+ "&pageSize=" + this.page.pageSize;        
          request.post(url, para).then((res) => {   
            if (res.resdata.length > 0) {
              this.isPage = true;
            } else {
              this.isPage = false;
            }
            this.page.totalCount = res.count;
            this.datalist = res.resdata;
            this.listLoading = false;
          });
        },    
                 //查询
        query() {
          this.getDatas();
        },  
            
    getdormbuildingList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/dormbuilding/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.dormbuildingList = res.resdata;
      });
    },
    
    getdormitoryList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/dormitory/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.dormitoryList = res.resdata;
      });
    },
   
        // 查看
        handleShow(index, row) {
          this.$router.push({
            path: "/WaterelectricityfeeDetail",
             query: {
                id: row.id,
              },
          });
        },
    
        // 编辑
        handleEdit(index, row) {
          this.$router.push({
            path: "/WaterelectricityfeeEdit",
             query: {
                id: row.id,
              },
          });
        },

        // 缴纳水电费
        handlePay(index, row) {
          this.currentPayRecord = row;
          this.currentPayAmount = row.amount;
          this.selectedPayMethod = '';
          this.payDialogVisible = true;
        },

        // 选择支付方式
        selectPayMethod(method) {
          this.selectedPayMethod = method;
        },

        // 确认支付
        confirmPayment() {
          this.payLoading = true;

          // 模拟支付处理时间
          setTimeout(() => {
            let url = base + "/waterelectricityfee/pay";
            let para = {
              id: this.currentPayRecord.id,
              payMethod: this.selectedPayMethod
            };

            request.post(url, para).then((res) => {
              this.payLoading = false;
              if (res.code == 200) {
                this.$message({
                  message: "缴纳成功！",
                  type: "success",
                  offset: 320,
                });
                this.payDialogVisible = false;
                this.getDatas(); // 刷新列表
              } else {
                this.$message({
                  message: res.msg || "缴纳失败，请重试",
                  type: "error",
                  offset: 320,
                });
              }
            }).catch(() => {
              this.payLoading = false;
              this.$message({
                message: "缴纳失败，请重试",
                type: "error",
                offset: 320,
              });
            });
          }, 1500); // 模拟1.5秒支付处理时间
        },
      },
}

</script>
<style scoped>
.pay-method {
  transition: all 0.3s ease;
}

.pay-method:hover {
  border-color: #409EFF !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.pay-method.selected {
  border-color: #409EFF !important;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.pay-method img {
  object-fit: contain;
}
</style>
 

