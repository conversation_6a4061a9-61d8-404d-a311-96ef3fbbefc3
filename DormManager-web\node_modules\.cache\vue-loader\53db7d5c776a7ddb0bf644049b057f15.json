{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue", "mtime": 1749049338470}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\components\\LeftMenu.vue"], "names": [], "mappings": ";AA+WA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC;IACJ;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC;MACF,CAAC,CAAC;;MAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;UACG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACvB,CAAC;EACH;AACF,CAAC", "file": "I:/product4/B7839DormManager/DormManager-web/src/components/LeftMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n <nav class=\"pcoded-navbar\">\r\n        <div class=\"navbar-wrapper\">\r\n            <div class=\"navbar-brand header-logo\">\r\n                <a href=\"/main\" class=\"b-brand\">\r\n                    <div class=\"b-bg\">\r\n                        <i class=\"feather icon-trending-up\"></i>\r\n                    </div>\r\n                    <span class=\"b-title\">宿舍管理系统</span>\r\n                </a>\r\n\r\n            </div>\r\n            <div class=\"navbar-content scroll-div\">\r\n                <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '管理员'\">\r\n                 \r\n                   \r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                                 <li> <router-link to=\"/dormbuildingAdd\">添加宿舍楼</router-link></li>\r\n  <li> <router-link to=\"/dormbuildingManage\">管理宿舍楼</router-link></li>\r\n                             <li> <router-link to=\"/dormitoryAdd\">添加宿舍</router-link></li>\r\n  <li> <router-link to=\"/dormitoryManage\">管理宿舍</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">学生管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentAdd\">添加学生</router-link></li>\r\n  <li> <router-link to=\"/studentManage\">管理学生</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿管阿姨管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/hostessAdd\">添加宿管阿姨</router-link></li>\r\n  <li> <router-link to=\"/hostessManage\">管理宿管阿姨</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">维修员管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairmenAdd\">添加维修员</router-link></li>\r\n  <li> <router-link to=\"/repairmenManage\">管理维修员</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/systemnoticesAdd\">添加公告</router-link></li>\r\n  <li> <router-link to=\"/systemnoticesManage\">管理公告</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitorychangeAdd\">添加宿舍更换</router-link></li>\r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         <li> <router-link to=\"/repairtypeAdd\">添加报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairtypeManage\">管理报修类型</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">管理报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage2\">报修列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                    \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n    \r\n\r\n  \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">添加离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">管理离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage2\">离校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">管理返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage2\">返校登记列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n \r\n    \r\n\r\n     \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">统计报表</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/total1\">报修类型统计</router-link></li>\r\n  <li> <router-link to=\"/total2\">维修员维修统计</router-link></li>\r\n  <li> <router-link to=\"/total3\">宿舍评分统计</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                      <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">系统管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                           \r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '学生'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                       <li> <router-link to=\"/mydormitory\">我的宿舍</router-link></li> \r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/leaveschoolAdd\">提交离校登记</router-link></li>\r\n  <li> <router-link to=\"/leaveschoolManage\">我的离校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                        <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/registerinfoAdd\">添加返校登记</router-link></li>\r\n  <li> <router-link to=\"/registerinfoManage\">我的返校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/repairordersAdd\">在线报修</router-link></li>\r\n  <li> <router-link to=\"/repairordersManage\">我的报修</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                        <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n          \r\n  <li> <router-link to=\"/waterelectricityfeeManage2\">水电费</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n     \r\n     \r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                    \r\n  <li> <router-link to=\"/dormitoryscoreManage2\">宿舍评分</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n   \r\n \r\n    \r\n   \r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/studentInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '维修员'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                        \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n    \r\n \r\n   \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">报修管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n          \r\n  <li> <router-link to=\"/repairordersManage2\">待处理报修</router-link></li>\r\n    <li> <router-link to=\"/repairordersManage3\">已处理报修</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n    \r\n\r\n                                                 <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                 <li> <router-link to=\"/repairmenInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n          \r\n                </ul>\r\n\r\n                      <ul class=\"nav pcoded-inner-navbar\" v-show=\"role == '宿管阿姨'\">\r\n                    <li class=\"nav-item pcoded-menu-caption\">\r\n                        <label>功能菜单</label>\r\n                    </li>\r\n                         <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">公告管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                        \r\n  <li> <router-link to=\"/systemnoticesManage2\">公告列表</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                           <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍更换管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                 \r\n  <li> <router-link to=\"/dormitorychangeManage\">管理宿舍更换</router-link></li>\r\n\r\n\r\n                        </ul>\r\n                    </li>\r\n   \r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">离校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                         \r\n\r\n  <li> <router-link to=\"/leaveschoolManage2\">管理离校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">返校登记管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                   \r\n  <li> <router-link to=\"/registerinfoManage2\">管理返校登记</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n                          <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">水电费管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/waterelectricityfeeAdd\">添加水电费</router-link></li>\r\n  <li> <router-link to=\"/waterelectricityfeeManage\">管理水电费</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n       <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">宿舍评分管理</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                             <li> <router-link to=\"/dormitoryscoreAdd\">添加宿舍评分</router-link></li>\r\n  <li> <router-link to=\"/dormitoryscoreManage\">管理宿舍评分</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n \r\n \r\n     \r\n                                              <li  class=\"nav-item pcoded-hasmenu\">\r\n                        <a href=\"#!\" class=\"nav-link\"><span class=\"pcoded-micon\"><i class=\"feather icon-gitlab\"></i></span><span class=\"pcoded-mtext\">个人中心</span></a>\r\n                        <ul class=\"pcoded-submenu\">\r\n                   <li> <router-link to=\"/hostessInfo\">修改个人信息</router-link></li>\r\n  <li> <router-link to=\"/password\">修改密码</router-link></li>\r\n\r\n                        </ul>\r\n                    </li>\r\n\r\n\r\n          \r\n                </ul>\r\n\r\n\r\n            </div>\r\n        </div>\r\n    </nav>\r\n    \r\n\r\n</template>\r\n\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n    };\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      this.activeMenu = to.name;\r\n      this.$nextTick(() => {\r\n        this.initializeMenu();\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.$nextTick(() => {\r\n      this.initializeMenu();\r\n    });\r\n  },\r\n  methods: {\r\n    initializeMenu() {\r\n      $('.nav-item.pcoded-hasmenu > a').off('click').on('click', function(e) {\r\n        e.preventDefault();\r\n        const $parent = $(this).parent();\r\n\r\n        if ($parent.hasClass('pcoded-trigger')) {\r\n          $parent.removeClass('pcoded-trigger');\r\n          $parent.children('ul').slideUp();\r\n        } else {\r\n          $('.nav-item.pcoded-hasmenu').removeClass('pcoded-trigger');\r\n          $('.nav-item.pcoded-hasmenu > ul').slideUp();\r\n          $parent.addClass('pcoded-trigger');\r\n          $parent.children('ul').slideDown();\r\n        }\r\n      });\r\n\r\n      // 初始化：根据当前路由展开对应的菜单\r\n      const currentPath = this.$route.path;\r\n      $('.pcoded-submenu a').each(function() {\r\n        if ($(this).attr('href') === currentPath) {\r\n          $(this).parents('.nav-item.pcoded-hasmenu').addClass('pcoded-trigger');\r\n          $(this).parents('ul.pcoded-submenu').show();\r\n        }\r\n      });\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem(\"userLname\");\r\n            sessionStorage.removeItem(\"role\");\r\n            _this.$router.push(\"/\");\r\n          })\r\n          .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.example-showcase .el-dropdown-link {\r\n  cursor: pointer;\r\n  color:green;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.pcoded-submenu a{\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n}\r\n\r\n/*加点击效果*/\r\n.pcoded-submenu a:hover{\r\n  color: #fff;\r\n  color: #ff6600;\r\n}\r\n</style>\r\n\r\n"]}]}