﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">

<el-form-item label="宿舍编号" prop="doro">
<el-select v-model="formData.doro" placeholder="请选择"  size="small">
<el-option v-for="item in dormitoryList" :key="item.doro" :label="item.doro" :value="item.doro"></el-option>
</el-select>
</el-form-item>
<el-form-item label="费用类型" prop="ftype">
<el-select v-model="formData.ftype" placeholder="请选择"  size="small">
<el-option label="水费" value="水费"></el-option>
<el-option label="电费" value="电费"></el-option>
</el-select>
</el-form-item>
<el-form-item label="费用标题" prop="title">
<el-input v-model="formData.title" placeholder="费用标题"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="费用金额" prop="amount">
<el-input v-model="formData.amount" placeholder="费用金额"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="备注说明" prop="note">
<el-input type="textarea" :rows="5" v-model="formData.note" placeholder="备注说明"  size="small"></el-input>
</el-form-item>
<el-form-item label="缴纳状态" prop="status">
<el-radio-group v-model="formData.status" placeholder="缴纳状态"  style="width:50%;" >
<el-radio label="未缴纳" value="未缴纳"></el-radio>
<el-radio label="已缴纳" value="已缴纳"></el-radio>
</el-radio-group>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'WaterelectricityfeeAdd',
  components: {
    
  },  
    data() {
      return {   
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {
          status: '未缴纳'
        }, //表单数据           
        addrules: {
          dbid: [{ required: true, message: '请选择宿舍楼', trigger: 'onchange' }],
          doro: [{ required: true, message: '请选择宿舍编号', trigger: 'onchange' }],
          ftype: [{ required: true, message: '请选择费用类型', trigger: 'onchange' }],
          title: [{ required: true, message: '请输入费用标题', trigger: 'blur' },
],          amount: [{ required: true, message: '请输入费用金额', trigger: 'blur' },
],         status: [{ required: true, message: '请输入缴纳状态', trigger: 'blur' },
],        },

      };
    },
    mounted() {
    
      this.getdormbuildingList();
      this.getdormitoryList();
    },

 
    methods: {    
   // 添加
    save() {       
         this.$refs["formDataRef"].validate((valid) => { //验证表单
           if (valid) {
             let url = base + "/waterelectricityfee/add";
             this.btnLoading = true;
             var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息
             this.formData.dbid = user.dbid;
             request.post(url, this.formData).then((res) => { //发送请求         
               if (res.code == 200) {
                 this.$message({
                   message: "操作成功",
                   type: "success",
                   offset: 320,
                 });              
                this.$router.push({
                path: "/WaterelectricityfeeManage",
                });
               } else {
                 this.$message({
                   message: res.msg,
                   type: "error",
                   offset: 320,
                 });
               }
               this.btnLoading=false;
             });
           }        
           
         });
    },
    
       // 返回
        goBack() {
          this.$router.push({
            path: "/WaterelectricityfeeManage",
          });
        },       
              
            
    getdormbuildingList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/dormbuilding/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.dormbuildingList = res.resdata;
      });
    },
    
      getdormitoryList() {
 var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息
        let dbid = user.dbid;
        let para = {
        dbid: dbid
      };
      this.listLoading = true;
      let url = base + "/dormitory/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.dormitoryList = res.resdata;
      });
    },
  
           
           
      },
}

</script>
<style scoped>
</style>
 

