<template>

  <div class="auth-wrapper aut-bg-img">
        <div class="auth-content"  style="width: 450px;">
             <form action="#">
            <div class="card" style="padding-top: 30px; padding-bottom: 30px; ">
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="feather icon-unlock auth-icon"></i>
                    </div>
                    <h3 class="mb-4">宿舍管理系统</h3>
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" placeholder="用户名" v-model="loginModel.username">
                    </div>
                    <div class="input-group mb-4">
                        <input type="password" class="form-control" placeholder="密码" v-model="loginModel.password">
                    </div>
                       <div class="form-group text-left">
                        <div class="checkbox checkbox-fill d-inline">
                          <el-radio label="管理员" v-model="loginModel.radio">管理员</el-radio>
                                   <el-radio label="学生" v-model="loginModel.radio">学生</el-radio>
      <el-radio label="维修员" v-model="loginModel.radio">维修员</el-radio>
      <el-radio label="宿管阿姨" v-model="loginModel.radio">宿管阿姨</el-radio>


                        </div>
                    </div>


            <el-button class="btn btn-primary shadow-2 mb-4"  @click="login">登录</el-button>
                     <a href="#"  @click="toreg"> 新学生注册 </a>
 <el-dialog title="学生注册"  v-model="formVisible" width="40%" :close-on-click-modal="false">
 <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="rules"  align="left">
<el-form-item label="学号" prop="sno">
<el-input v-model="formData.sno" placeholder="学号"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="登录密码" prop="password">
<el-input type="password" v-model="formData.password" placeholder="登录密码"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="确认密码" prop="password2">
<el-input type="password" v-model="formData.password2" placeholder="确认密码"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="姓名" prop="stname">
<el-input v-model="formData.stname" placeholder="姓名"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="性别" prop="sex">
<el-radio-group v-model="formData.sex" @change="onGenderChange">
<el-radio label="男">
男
</el-radio>
<el-radio label="女">
女
</el-radio>
</el-radio-group>
</el-form-item>
<el-form-item label="手机号码" prop="phone">
<el-input v-model="formData.phone" placeholder="手机号码"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="宿舍楼" prop="dbid">
<el-select v-model="formData.dbid" placeholder="请选择"  size="small" @change="onBuildingChange">
<el-option v-for="item in dormbuildingList" :key="item.dbid" :label="item.dbname" :value="item.dbid"></el-option>
</el-select>
</el-form-item>
<el-form-item label="宿舍" prop="doro">
<el-select v-model="formData.doro" placeholder="请先选择宿舍楼和性别"  size="small" :disabled="!formData.dbid || !formData.sex">
<el-option v-for="item in filteredDormitoryList" :key="item.doro" :label="item.doro" :value="item.doro"></el-option>
</el-select>
</el-form-item>
<el-form-item label="专业" prop="specialty">
<el-input v-model="formData.specialty" placeholder="专业"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="班级" prop="clsname">
<el-input v-model="formData.clsname" placeholder="班级"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item>
<el-button type="primary" @click="reg" :loading="btnLoading" >注 册</el-button>
</el-form-item>
</el-form>
</el-dialog>


                </div>
            </div>
            </form>

        </div>
    </div>



</template>
<script>
import request, { base } from "../../utils/http";
export default {
  name: "Login",
  data() {
    return {
      year: new Date().getFullYear(),
      loginModel: {
        username: "",
        password: "",
        radio: "管理员",
      },
      loginModel2: {},
     add: true, //是否是添加
      formVisible: false,
      formData:{},
      dormbuildingList: [], //宿舍楼列表
      dormitoryList: [], //所有宿舍列表
      filteredDormitoryList: [], //过滤后的宿舍列表

      addrules: {
          sno: [{ required: true, message: '请输入学号', trigger: 'blur' },],
          password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
          password2: [{ required: true, message: '请输入登录密码', trigger: 'blur' },{ validator: (rule, value, callback) => { if (value !== this.formData.password) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],
          stname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],
          sex: [{ required: true, message: '请输入性别', trigger: 'blur' },],
          phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' },        { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },],
          doro: [{ required: true, message: '请选择宿舍', trigger: 'onchange' }],
          specialty: [{ required: true, message: '请输入专业', trigger: 'blur' },],
          clsname: [{ required: true, message: '请输入班级', trigger: 'blur' },],
      },


      btnLoading: false, //按钮是否在加载中


    };
  },
  mounted() {},
  created() {

  },
  methods: {
    login() {
      let that = this;

      if (that.loginModel.username == "") {
        that.$message({
          message: "请输入账号",
          type: "warning",
        });
        return;
      }
      if (that.loginModel.password == "") {
        that.$message({
          message: "请输入密码",
          type: "warning",
        });
        return;
      }

      this.loading = true;
     var role = that.loginModel.radio; //获取身份
if (role == '管理员') {
      let url = base + "/admin/login";
      this.loginModel2.aname = this.loginModel.username;
      this.loginModel2.loginpassword = this.loginModel.password;
      request.post(url, this.loginModel2).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          console.log(JSON.stringify(res.resdata));
          sessionStorage.setItem("user", JSON.stringify(res.resdata));
          sessionStorage.setItem("userLname", res.resdata.aname);
          sessionStorage.setItem("role", "管理员");
          this.$router.push("/main");
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
          }
else if (role == '宿管阿姨') {
      let url = base + "/hostess/login";
      this.loginModel2.hno = this.loginModel.username;
      this.loginModel2.password = this.loginModel.password;
      request.post(url, this.loginModel2).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          console.log(JSON.stringify(res.resdata));
          sessionStorage.setItem("user", JSON.stringify(res.resdata));
          sessionStorage.setItem("userLname", res.resdata.hno);
          sessionStorage.setItem("role", "宿管阿姨");
          this.$router.push("/main");
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
          }
else if (role == '维修员') {
      let url = base + "/repairmen/login";
      this.loginModel2.rno = this.loginModel.username;
      this.loginModel2.password = this.loginModel.password;
      request.post(url, this.loginModel2).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          console.log(JSON.stringify(res.resdata));
          sessionStorage.setItem("user", JSON.stringify(res.resdata));
          sessionStorage.setItem("userLname", res.resdata.rno);
          sessionStorage.setItem("role", "维修员");
          this.$router.push("/main");
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
          }
else if (role == '学生') {
      let url = base + "/student/login";
      this.loginModel2.sno = this.loginModel.username;
      this.loginModel2.password = this.loginModel.password;
      request.post(url, this.loginModel2).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          console.log(JSON.stringify(res.resdata));
          sessionStorage.setItem("user", JSON.stringify(res.resdata));
          sessionStorage.setItem("userLname", res.resdata.sno);
          sessionStorage.setItem("role", "学生");
          this.$router.push("/main");
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
          }


    },

    toreg() {
    this.formVisible = true;
    this.add = true;
    this.isClear = true;
    this.rules = this.addrules;
    this.getdormbuildingList(); // 加载宿舍楼列表
    this.$nextTick(() => {
        this.$refs["formDataRef"].resetFields();
    });
},

//注册
reg() {
    //表单验证
    this.$refs["formDataRef"].validate((valid) => {

        if (valid) {
            let url = base + "/student/add"; //请求地址
            this.btnLoading = true; //按钮加载状态
            request.post(url, this.formData).then((res) => { //请求接口
                if (res.code == 200) {
                    this.$message({
                        message: "恭喜您，注册成功，请登录！",
                        type: "success",
                        offset: 320,
                    });
                    this.formVisible = false; //关闭表单
                    this.btnLoading = false; //按钮加载状态
                    this.$refs["formDataRef"].resetFields(); //重置表单
                    this.$refs["formDataRef"].clearValidate();
                }
                else if (res.code == 201) {
                    this.$message({
                        message: res.msg,
                        type: "error",
                        offset: 320,
                    });
                }
                else {
                    this.$message({
                        message: "服务器错误",
                        type: "error",
                        offset: 320,
                    });
                }
            });
        }
    });
},
// 获取宿舍楼列表
getdormbuildingList() {
  let para = {};
  let url = base + "/dormbuilding/list?currentPage=1&pageSize=1000";
  request.post(url, para).then((res) => {
    this.dormbuildingList = res.resdata;
  });
},

// 宿舍楼变化时的处理
onBuildingChange() {
  this.formData.doro = ''; // 清空宿舍选择
  this.updateDormitoryList();
},

// 性别变化时的处理
onGenderChange() {
  this.formData.doro = ''; // 清空宿舍选择
  this.updateDormitoryList();
},

// 更新宿舍列表
updateDormitoryList() {
  if (!this.formData.dbid || !this.formData.sex) {
    this.filteredDormitoryList = [];
    return;
  }

  let para = {
    dbid: this.formData.dbid,
    dorgender: this.formData.sex
  };
  let url = base + "/dormitory/listByBuildingAndGender";
  request.post(url, para).then((res) => {
    if (res.code == 200) {
      this.filteredDormitoryList = res.resdata;
    } else {
      this.filteredDormitoryList = [];
      this.$message({
        message: res.msg || "获取宿舍列表失败",
        type: "error",
        offset: 320,
      });
    }
  }).catch(() => {
    this.filteredDormitoryList = [];
    this.$message({
      message: "获取宿舍列表失败",
      type: "error",
      offset: 320,
    });
  });
},





  },
};
</script>

<style scoped>
@import url(../assets/css/htstyle.css);

</style>


