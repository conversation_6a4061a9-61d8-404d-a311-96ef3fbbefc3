{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total2.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total2.vue", "mtime": 1749044636470}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgogaW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOwogaW1wb3J0ICogYXMgZWNoYXJ0cyBmcm9tICdlY2hhcnRzJzsKIAogZXhwb3J0IGRlZmF1bHQgewogICBkYXRhKCkgewogICAgIHJldHVybiB7CiAgICAgICBwaWVEYXRhOiBbXSwKICAgICAgIHBpZU5hbWU6IFtdLAogICAgIH07CiAgIH0sCiAKICAgbW91bnRlZCgpIHsKICAgICB0aGlzLmdldGRhdGEoKTsKICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5yZXNpemVDaGFydCk7CiAgIH0sCiAgIGJlZm9yZURlc3Ryb3koKSB7CiAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQ2hhcnQpOwogICB9LAogICBtZXRob2RzOiB7CiAgICAgLy8g5pWw5o2u5Yid5aeL5YyWCiAgICAgZ2V0ZGF0YSgpIHsKICAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9SZXBvcnREYXRhL3F1ZXJ5UmVwb3J0MyI7CiAKICAgICAgIGxldCBwYXJhID0ge307CiAKICAgICAgIHJlcXVlc3QucG9zdCh1cmwsIHBhcmEpLnRoZW4oKHJlcykgPT4gewogICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICAgdmFyIHNzID0gcmVzLnJlc2RhdGE7CiAgICAgICAgICAgdmFyIHBpZU5hbWUyID0gW107CiAgICAgICAgICAgdmFyIHBpZURhdGEyID0gW107CiAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzcy5sZW5ndGg7IGkrKykgewogICAgICAgICAgICAgcGllTmFtZTJbaV0gPSBzc1tpXS5uYW1lOwogICAgICAgICAgICAgcGllRGF0YTJbaV0gPSBzc1tpXS5udW07CiAgICAgICAgICAgfQogICAgICAgICAgIHRoaXMucGllTmFtZSA9IHBpZU5hbWUyOwogICAgICAgICAgIHRoaXMucGllRGF0YSA9IHBpZURhdGEyOwogICAgICAgICAgIHRoaXMuaW5pdEVjaGFydHMoKTsKICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOwogICAgICAgICB9CiAgICAgICB9KTsKICAgICB9LAogCiAgICAgaW5pdEVjaGFydHMoKSB7CiAgICAgICBjb25zdCBkb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY29udGFpbmVyJyk7CiAgICAgICBjb25zdCBteUNoYXJ0ID0gZWNoYXJ0cy5pbml0KGRvbSwgbnVsbCwgeyByZW5kZXJlcjogJ2NhbnZhcycgfSk7CiAKICAgICAgIGNvbnN0IG9wdGlvbiA9IHsKICAgICAgICAgdGl0bGU6IHsKIAogICAgICAgICB9LAogICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLAogICAgICAgICAgIGF4aXNQb2ludGVyOiB7CiAgICAgICAgICAgICB0eXBlOiAnc2hhZG93JwogICAgICAgICAgIH0KICAgICAgICAgfSwKICAgICAgICAgbGVnZW5kOiB7fSwKICAgICAgICAgZ3JpZDogewogICAgICAgICAgIGxlZnQ6ICczJScsCiAgICAgICAgICAgcmlnaHQ6ICc0JScsCiAgICAgICAgICAgYm90dG9tOiAnMyUnLAogICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQogICAgICAgICB9LAogICAgICAgICB4QXhpczogewogICAgICAgICAgIHR5cGU6ICd2YWx1ZScsCiAgICAgICAgICAgYm91bmRhcnlHYXA6IFswLCAwLjAxXQogICAgICAgICB9LAogICAgICAgICB5QXhpczogewogICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsCiAgICAgICAgICAgZGF0YTogdGhpcy5waWVOYW1lCiAgICAgICAgIH0sCiAgICAgICAgIHNlcmllczogWwogICAgICAgICAgIHsKICAgICAgICAgICAgIG5hbWU6ICfmgLvplIDllK7mlbDph48nLAogICAgICAgICAgICAgdHlwZTogJ2JhcicsCiAgICAgICAgICAgICBkYXRhOiB0aGlzLnBpZURhdGEKICAgICAgICAgICB9CiAgICAgICAgIF0KICAgICAgIH07CiAKICAgICAgIGlmIChvcHRpb24gJiYgdHlwZW9mIG9wdGlvbiA9PT0gJ29iamVjdCcpIHsKICAgICAgICAgbXlDaGFydC5zZXRPcHRpb24ob3B0aW9uKTsKICAgICAgIH0KICAgICB9LAogICAgIHJlc2l6ZUNoYXJ0KCkgewogICAgICAgY29uc3QgZG9tID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2NvbnRhaW5lcicpOwogICAgICAgY29uc3QgbXlDaGFydCA9IGVjaGFydHMuZ2V0SW5zdGFuY2VCeURvbShkb20pOwogICAgICAgbXlDaGFydC5yZXNpemUoKTsKICAgICB9CiAgIH0KIH07Cg=="}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total2.vue"], "names": [], "mappings": ";;CASC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;CAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;KACb,CAAC;GACH,CAAC;;GAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACrD,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACxD,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACR,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;OAE3C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;OAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;SACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;WACnB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;WACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;WACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;aAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACzB;WACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACpB,EAAE,CAAC,CAAC,CAAC,EAAE;WACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9B;OACF,CAAC,CAAC;KACJ,CAAC;;KAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;OAE/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;SACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;SAEP,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACf;SACF,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACV,CAAC,CAAC,CAAC,CAAC,EAAE;WACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;WACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;WACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;WACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACnB,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACvB,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACnB,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACN;aACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;aACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACnB;SACF;OACF,CAAC;;OAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAC3B;KACF,CAAC;KACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAClB;GACF;CACF,CAAC", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/total/Total2.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n       <div id=\"container\" :style=\"{ height: '600px' }\"></div>\n \n    </div>\n</template>\n\n<script>\n\n import request, { base } from \"../../../../utils/http\";\n import * as echarts from 'echarts';\n \n export default {\n   data() {\n     return {\n       pieData: [],\n       pieName: [],\n     };\n   },\n \n   mounted() {\n     this.getdata();\n     window.addEventListener('resize', this.resizeChart);\n   },\n   beforeDestroy() {\n     window.removeEventListener('resize', this.resizeChart);\n   },\n   methods: {\n     // 数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport3\";\n \n       let para = {};\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n           var pieName2 = [];\n           var pieData2 = [];\n           for (let i = 0; i < ss.length; i++) {\n             pieName2[i] = ss[i].name;\n             pieData2[i] = ss[i].num;\n           }\n           this.pieName = pieName2;\n           this.pieData = pieData2;\n           this.initEcharts();\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n     },\n \n     initEcharts() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.init(dom, null, { renderer: 'canvas' });\n \n       const option = {\n         title: {\n \n         },\n         tooltip: {\n           trigger: 'axis',\n           axisPointer: {\n             type: 'shadow'\n           }\n         },\n         legend: {},\n         grid: {\n           left: '3%',\n           right: '4%',\n           bottom: '3%',\n           containLabel: true\n         },\n         xAxis: {\n           type: 'value',\n           boundaryGap: [0, 0.01]\n         },\n         yAxis: {\n           type: 'category',\n           data: this.pieName\n         },\n         series: [\n           {\n             name: '总销售数量',\n             type: 'bar',\n             data: this.pieData\n           }\n         ]\n       };\n \n       if (option && typeof option === 'object') {\n         myChart.setOption(option);\n       }\n     },\n     resizeChart() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.getInstanceByDom(dom);\n       myChart.resize();\n     }\n   }\n };\n</script>\n<style scoped>\n</style>\n \n\n"]}]}