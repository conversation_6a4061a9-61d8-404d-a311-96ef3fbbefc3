{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue?vue&type=style&index=0&id=05e4597b&scoped=true&lang=css", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue", "mtime": 1749047102541}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749043012394}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749043013744}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749043012910}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5ib3gtY2FyZCB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmNsZWFyZml4OmJlZm9yZSwKLmNsZWFyZml4OmFmdGVyIHsKICBkaXNwbGF5OiB0YWJsZTsKICBjb250ZW50OiAiIjsKfQoKLmNsZWFyZml4OmFmdGVyIHsKICBjbGVhcjogYm90aDsKfQo="}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue"], "names": [], "mappings": ";AAiUA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/student/MyDormitory.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div>\n    <!-- 当前宿舍信息 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">我的宿舍信息</span>\n      </div>\n      <div v-if=\"currentDormitory.doro\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <p><strong>宿舍楼：</strong>{{ currentDormitory.dbname }}</p>\n            <p><strong>宿舍编号：</strong>{{ currentDormitory.doro }}</p>\n          </el-col>\n       \n        </el-row>\n      </div>\n      <div v-else>\n        <p style=\"color: #999;\">暂未分配宿舍</p>\n      </div>\n    </el-card>\n\n    <!-- 申请更换宿舍 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请更换宿舍</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"showApplyForm = !showApplyForm\">\n          {{ showApplyForm ? '收起' : '展开' }}\n        </el-button>\n      </div>\n      <div v-show=\"showApplyForm\">\n        <el-form :model=\"applyForm\" :rules=\"applyRules\" ref=\"applyFormRef\" label-width=\"120px\">\n          <el-form-item label=\"新宿舍楼\" prop=\"dbid2\">\n            <el-select v-model=\"applyForm.dbid2\" placeholder=\"请选择宿舍楼\" @change=\"onBuildingChange\" style=\"width: 300px;\">\n              <el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"新宿舍\" prop=\"doro2\">\n            <el-select v-model=\"applyForm.doro2\" placeholder=\"请先选择宿舍楼\" :disabled=\"!applyForm.dbid2\" style=\"width: 300px;\">\n              <el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"申请原因\" prop=\"applicationreason\">\n            <el-input type=\"textarea\" v-model=\"applyForm.applicationreason\" placeholder=\"请输入申请更换宿舍的原因\" :rows=\"4\" style=\"width: 500px;\"></el-input>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"submitApplication\" :loading=\"btnLoading\">提交申请</el-button>\n            <el-button @click=\"resetApplyForm\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n    </el-card>\n\n    <!-- 申请历史 -->\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请历史</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"getApplicationHistory\">刷新</el-button>\n      </div>\n      <el-table :data=\"applicationList\" style=\"width: 100%\" v-loading=\"listLoading\">\n        <el-table-column prop=\"submissiontime\" label=\"申请时间\" width=\"150\"></el-table-column>\n        <el-table-column label=\"原宿舍\" width=\"150\">\n          <template #default=\"scope\">\n            {{ scope.row.dbname || '宿舍楼' + scope.row.dbid }} - {{ scope.row.doro }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"目标宿舍\" width=\"150\">\n          <template #default=\"scope\">\n            {{ scope.row.dbname2 || '宿舍楼' + scope.row.dbid2 }} - {{ scope.row.doro2 }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"applicationreason\" label=\"申请原因\" show-overflow-tooltip></el-table-column>\n        <el-table-column prop=\"reviewstatus\" label=\"审核状态\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getStatusType(scope.row.reviewstatus)\">{{ scope.row.reviewstatus }}</el-tag>\n          </template>\n        </el-table-column>\n<el-table-column label=\"操作\" min-width=\"250\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n      \n      </el-table>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'MyDormitory',\n  data() {\n    return {\n      currentDormitory: {}, // 当前宿舍信息\n      showApplyForm: false, // 是否显示申请表单\n      btnLoading: false, // 提交按钮加载状态\n      listLoading: false, // 列表加载状态\n      \n      // 申请表单\n      applyForm: {\n        sno: '',\n        dbid: null, // 原宿舍楼ID\n        doro: '', // 原宿舍编号\n        dbid2: null, // 新宿舍楼ID\n        doro2: '', // 新宿舍编号\n        applicationreason: ''\n      },\n      \n      // 表单验证规则\n      applyRules: {\n        dbid2: [{ required: true, message: '请选择新宿舍楼', trigger: 'change' }],\n        doro2: [{ required: true, message: '请选择新宿舍', trigger: 'change' }],\n        applicationreason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]\n      },\n      \n      // 数据列表\n      dormbuildingList: [], // 宿舍楼列表\n      filteredDormitoryList: [], // 过滤后的宿舍列表\n      applicationList: [], // 申请历史列表\n      \n      // 学生信息\n      studentInfo: {}\n    };\n  },\n  \n  created() {\n    this.getStudentInfo();\n    this.getDormbuildingList();\n    this.getApplicationHistory();\n  },\n  \n  methods: {\n    // 获取学生信息和当前宿舍信息\n    getStudentInfo() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.studentInfo = user;\n      this.applyForm.sno = user.sno;\n\n      let url = base + \"/student/get?id=\" + user.sno;\n      request.post(url, {}).then((res) => {\n        if (res.code == 200) {\n          this.currentDormitory = res.resdata;\n          // 设置原宿舍信息\n          this.applyForm.dbid = res.resdata.dbid;\n          this.applyForm.doro = res.resdata.doro;\n        }\n      });\n    },\n    \n    // 获取宿舍楼列表\n    getDormbuildingList() {\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n      request.post(url, {}).then((res) => {\n        this.dormbuildingList = res.resdata;\n      });\n    },\n    \n    // 宿舍楼变化时的处理\n    onBuildingChange() {\n      this.applyForm.doro2 = ''; // 清空宿舍选择\n      this.updateDormitoryList();\n    },\n    \n    // 更新宿舍列表（根据宿舍楼和学生性别过滤）\n    updateDormitoryList() {\n      if (!this.applyForm.dbid2 || !this.studentInfo.sex) {\n        this.filteredDormitoryList = [];\n        return;\n      }\n\n      let para = {\n        dbid: this.applyForm.dbid2,\n        dorgender: this.studentInfo.sex\n      };\n      let url = base + \"/dormitory/listByBuildingAndGender\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          // 过滤掉当前宿舍\n          this.filteredDormitoryList = res.resdata.filter(item => \n            !(item.dbid == this.applyForm.dbid && item.doro == this.applyForm.doro)\n          );\n        } else {\n          this.filteredDormitoryList = [];\n          this.$message({\n            message: res.msg || \"获取宿舍列表失败\",\n            type: \"error\"\n          });\n        }\n      }).catch(() => {\n        this.filteredDormitoryList = [];\n        this.$message({\n          message: \"获取宿舍列表失败\",\n          type: \"error\"\n        });\n      });\n    },\n    \n    // 提交申请\n    submitApplication() {\n      this.$refs.applyFormRef.validate((valid) => {\n        if (valid) {\n          // 检查是否选择了不同的宿舍\n          if (this.applyForm.dbid == this.applyForm.dbid2 && this.applyForm.doro == this.applyForm.doro2) {\n            this.$message({\n              message: \"新宿舍不能与当前宿舍相同\",\n              type: \"warning\"\n            });\n            return;\n          }\n          \n          this.btnLoading = true;\n          let url = base + \"/dormitorychange/apply\";\n          request.post(url, this.applyForm).then((res) => {\n            if (res.code == 200) {\n              this.$message({\n                message: \"申请提交成功，请等待审核\",\n                type: \"success\"\n              });\n              this.resetApplyForm();\n              this.showApplyForm = false;\n              this.getApplicationHistory(); // 刷新申请历史\n            } else {\n              this.$message({\n                message: res.msg || \"申请提交失败\",\n                type: \"error\"\n              });\n            }\n            this.btnLoading = false;\n          }).catch(() => {\n            this.$message({\n              message: \"申请提交失败\",\n              type: \"error\"\n            });\n            this.btnLoading = false;\n          });\n        }\n      });\n    },\n    \n    // 重置申请表单\n    resetApplyForm() {\n      this.$refs.applyFormRef.resetFields();\n      this.applyForm.dbid2 = null;\n      this.applyForm.doro2 = '';\n      this.applyForm.applicationreason = '';\n      this.filteredDormitoryList = [];\n    },\n    \n    // 获取申请历史\n    getApplicationHistory() {\n      this.listLoading = true;\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      let para = {\n        sno: user ? user.sno : this.studentInfo.sno\n      };\n      let url = base + \"/dormitorychange/list?currentPage=1&pageSize=100\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          this.applicationList = res.resdata || [];\n        }\n        this.listLoading = false;\n      }).catch(() => {\n        this.listLoading = false;\n      });\n    },\n\n         // 删除宿舍更换\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/dormitorychange/del?id=\" + row.id;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n    },\n\n             \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/DormitorychangeDetail\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n    \n    // 获取状态标签类型\n    getStatusType(status) {\n      switch (status) {\n        case '待审核':\n          return 'warning';\n        case '审核通过':\n          return 'success';\n        case '审核不通过':\n          return 'danger';\n        default:\n          return 'info';\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin-bottom: 20px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n</style>\n"]}]}