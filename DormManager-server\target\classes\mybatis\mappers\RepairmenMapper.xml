<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.RepairmenMapper">
	<select id="findRepairmenList"  resultType="Repairmen">
		select * from repairmen 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Repairmen">
	    select  *  
        from repairmen a  left join repairtype b on a.typeid=b.typeid  	
		<where>
      		<if test="rno != null and rno != ''">
		    and a.rno = #{rno}
		</if>
		<if test="password != null and password != ''">
		    and a.password = #{password}
		</if>
		<if test="rnname != null and rnname != ''">
		    and a.rnname = #{rnname}
		</if>
		<if test="age != null and age !=0 ">
		    and a.age = #{age}
		</if>
		<if test="phone != null and phone != ''">
		    and a.phone = #{phone}
		</if>
		<if test="address != null and address != ''">
		    and a.address = #{address}
		</if>
		<if test="typeid != null and typeid !=0 ">
		    and a.typeid = #{typeid}
		</if>
		<if test="addtime != null and addtime != ''">
		    and a.addtime = #{addtime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} rno desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from repairmen a  left join repairtype b on a.typeid=b.typeid  
		<where>
      		<if test="rno != null and rno != ''">
		    and a.rno = #{rno}
		</if>
		<if test="password != null and password != ''">
		    and a.password = #{password}
		</if>
		<if test="rnname != null and rnname != ''">
		    and a.rnname = #{rnname}
		</if>
		<if test="age != null and age !=0 ">
		    and a.age = #{age}
		</if>
		<if test="phone != null and phone != ''">
		    and a.phone = #{phone}
		</if>
		<if test="address != null and address != ''">
		    and a.address = #{address}
		</if>
		<if test="typeid != null and typeid !=0 ">
		    and a.typeid = #{typeid}
		</if>
		<if test="addtime != null and addtime != ''">
		    and a.addtime = #{addtime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryRepairmenById" parameterType="String" resultType="Repairmen">
    select  *
     from repairmen a  left join repairtype b on a.typeid=b.typeid  	 where a.rno=#{value}
  </select>

  	<select id="findRepairmenWithLeastOrders" parameterType="Integer" resultType="Repairmen">
    select rm.*, rt.typename
    from repairmen rm
    left join repairtype rt on rm.typeid = rt.typeid
    left join (
        select rno, count(*) as order_count
        from repairorders
        group by rno
    ) ro on rm.rno = ro.rno
    where rm.typeid = #{typeid}
    order by IFNULL(ro.order_count, 0) asc
    limit 1
  </select>

	<insert id="insertRepairmen" useGeneratedKeys="true" keyProperty="rno" parameterType="Repairmen">
    insert into repairmen
    (rno,password,rnname,age,phone,address,typeid,addtime)
    values
    (#{rno},#{password},#{rnname},#{age},#{phone},#{address},#{typeid},now());
  </insert>
	
	<update id="updateRepairmen" parameterType="Repairmen" >
    update repairmen 
    <set>
		<if test="rno != null and rno != ''">
		    rno = #{rno},
		</if>
		<if test="password != null and password != ''">
		    password = #{password},
		</if>
		<if test="rnname != null and rnname != ''">
		    rnname = #{rnname},
		</if>
		<if test="age != null ">
		    age = #{age},
		</if>
		<if test="phone != null and phone != ''">
		    phone = #{phone},
		</if>
		<if test="address != null and address != ''">
		    address = #{address},
		</if>
		<if test="typeid != null ">
		    typeid = #{typeid},
		</if>
		<if test="addtime != null and addtime != ''">
		    addtime = #{addtime},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="rno != null or rno != ''">
      rno=#{rno}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteRepairmen" parameterType="String">
    delete from  repairmen where rno=#{value}
  </delete>

	
	
</mapper>

 
