package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Repairmen;

public interface RepairmenMapper {

	//返回所有记录
	public List<Repairmen> findRepairmenList();
	
	//查询多条记录
	public List<Repairmen> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertRepairmen(Repairmen repairmen);

	//根据ID删除
	public int deleteRepairmen(String id);
	
	//更新
	public int updateRepairmen(Repairmen repairmen);
	
	//根据ID得到对应的记录
	public Repairmen queryRepairmenById(String id);

	//根据维修类型查找维修单最少的维修员
	public Repairmen findRepairmenWithLeastOrders(Integer typeid);

}

