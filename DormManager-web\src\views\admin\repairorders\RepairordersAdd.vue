﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">

<el-form-item label="报修类型" prop="typeid">
<el-select v-model="formData.typeid" placeholder="请选择"  size="small">
<el-option v-for="item in repairtypeList" :key="item.typeid" :label="item.typename" :value="item.typeid"></el-option>
</el-select>
</el-form-item>
<el-form-item label="报修主题" prop="tsubject">
<el-input v-model="formData.tsubject" placeholder="报修主题"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="具体描述" prop="description">
<WangEditor  ref="wangEditorRef" v-model="formData.description" :config="editorConfig"   :isClear="isClear" @change="editorChange"></WangEditor>
</el-form-item>


<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
import WangEditor from "../../../components/WangEditor";
export default {
  name: 'RepairordersAdd',
  components: {
    WangEditor,
  },  
    data() {
      return {   
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          typeid: [{ required: true, message: '请选择报修类型', trigger: 'change' },
],          tsubject: [{ required: true, message: '请输入报修主题', trigger: 'blur' },
],          description: [{ required: true, message: '请输入具体描述', trigger: 'blur' },
],        },

      };
    },
    mounted() {
    
      this.getrepairtypeList();
    },

 
    methods: {    
   // 添加
    save() {       
         this.$refs["formDataRef"].validate((valid) => { //验证表单
           if (valid) {
             let url = base + "/repairorders/add";
             this.btnLoading = true;
             var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息
             this.formData.sno = user.sno;
             this.formData.progress = "待处理";
             request.post(url, this.formData).then((res) => { //发送请求         
               if (res.code == 200) {
                 this.$message({
                   message: "提交成功，请等待处理",
                   type: "success",
                   offset: 320,
                 });              
                this.$router.push({
                path: "/RepairordersManage",
                });
               } else {
                 this.$message({
                   message: res.msg,
                   type: "error",
                   offset: 320,
                 });
               }
               this.btnLoading=false;
             });
           }        
           
         });
    },
    
       // 返回
        goBack() {
          this.$router.push({
            path: "/RepairordersManage",
          });
        },       
              
            
    getrepairtypeList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/repairtype/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.repairtypeList = res.resdata;
      });
    },
  
           
            // 富文本编辑器
    editorChange(val) {
      this.formData.description = val;
    },
   
      },
}

</script>
<style scoped>
</style>
 

