{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\repairorders\\RepairordersAdd.vue?vue&type=template&id=7b4fc86c", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\repairorders\\RepairordersAdd.vue", "mtime": 1749048547783}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_select", "typeid", "$event", "placeholder", "size", "_Fragment", "_renderList", "_ctx", "repairtypeList", "item", "_createBlock", "_component_el_option", "key", "typename", "value", "_component_el_input", "tsubject", "_component_WangEditor", "description", "config", "editorConfig", "isClear", "onChange", "$options", "<PERSON><PERSON><PERSON><PERSON>", "_component_el_button", "type", "onClick", "save", "loading", "btnLoading", "icon", "goBack"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\repairorders\\RepairordersAdd.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n\r\n<el-form-item label=\"报修类型\" prop=\"typeid\">\r\n<el-select v-model=\"formData.typeid\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option v-for=\"item in repairtypeList\" :key=\"item.typeid\" :label=\"item.typename\" :value=\"item.typeid\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"报修主题\" prop=\"tsubject\">\r\n<el-input v-model=\"formData.tsubject\" placeholder=\"报修主题\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"具体描述\" prop=\"description\">\r\n<WangEditor  ref=\"wangEditorRef\" v-model=\"formData.description\" :config=\"editorConfig\"   :isClear=\"isClear\" @change=\"editorChange\"></WangEditor>\r\n</el-form-item>\r\n\r\n\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\nimport WangEditor from \"../../../components/WangEditor\";\r\nexport default {\r\n  name: 'RepairordersAdd',\r\n  components: {\r\n    WangEditor,\r\n  },  \r\n    data() {\r\n      return {   \r\n        uploadVisible: false, \r\n        btnLoading: false, //保存按钮加载状态     \r\n        formData: {}, //表单数据           \r\n        addrules: {\r\n          typeid: [{ required: true, message: '请选择报修类型', trigger: 'change' },\r\n],          tsubject: [{ required: true, message: '请输入报修主题', trigger: 'blur' },\r\n],          description: [{ required: true, message: '请输入具体描述', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n    mounted() {\r\n    \r\n      this.getrepairtypeList();\r\n    },\r\n\r\n \r\n    methods: {    \r\n   // 添加\r\n    save() {       \r\n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n           if (valid) {\r\n             let url = base + \"/repairorders/add\";\r\n             this.btnLoading = true;\r\n             var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息\r\n             this.formData.sno = user.sno;\r\n             this.formData.progress = \"待处理\";\r\n             this.formData.doro = user.doro;\r\n             request.post(url, this.formData).then((res) => { //发送请求         \r\n               if (res.code == 200) {\r\n                 this.$message({\r\n                   message: \"提交成功，请等待处理\",\r\n                   type: \"success\",\r\n                   offset: 320,\r\n                 });              \r\n                this.$router.push({\r\n                path: \"/RepairordersManage\",\r\n                });\r\n               } else {\r\n                 this.$message({\r\n                   message: res.msg,\r\n                   type: \"error\",\r\n                   offset: 320,\r\n                 });\r\n               }\r\n               this.btnLoading=false;\r\n             });\r\n           }        \r\n           \r\n         });\r\n    },\r\n    \r\n       // 返回\r\n        goBack() {\r\n          this.$router.push({\r\n            path: \"/RepairordersManage\",\r\n          });\r\n        },       \r\n              \r\n            \r\n    getrepairtypeList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/repairtype/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.repairtypeList = res.resdata;\r\n      });\r\n    },\r\n  \r\n           \r\n            // 富文本编辑器\r\n    editorChange(val) {\r\n      this.formData.description = val;\r\n    },\r\n   \r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;iDAiBiC,KAAG;iDAC5B,KAAG;;;;;;;;;uBAlBvEC,mBAAA,CAuBM,OAvBNC,UAuBM,GAtBHC,YAAA,CAmBGC,kBAAA;IAnBOC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAGC,KAAK,EAAC;;sBAE/F,MAIe,CAJfR,YAAA,CAIeS,uBAAA;MAJDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAEY,CAFZX,YAAA,CAEYY,oBAAA;oBAFQT,KAAA,CAAAC,QAAQ,CAACS,MAAM;mEAAfV,KAAA,CAAAC,QAAQ,CAACS,MAAM,GAAAC,MAAA;QAAEC,WAAW,EAAC,KAAK;QAAEC,IAAI,EAAC;;0BAClD,MAA8B,E,kBAAzClB,mBAAA,CAAqHmB,SAAA,QAAAC,WAAA,CAA3FC,IAAA,CAAAC,cAAc,EAAtBC,IAAI;+BAAtBC,YAAA,CAAqHC,oBAAA;YAA1EC,GAAG,EAAEH,IAAI,CAACR,MAAM;YAAGH,KAAK,EAAEW,IAAI,CAACI,QAAQ;YAAGC,KAAK,EAAEL,IAAI,CAACR;;;;;;;QAGjGb,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAyF,CAAzFX,YAAA,CAAyF2B,mBAAA;oBAAtExB,KAAA,CAAAC,QAAQ,CAACwB,QAAQ;mEAAjBzB,KAAA,CAAAC,QAAQ,CAACwB,QAAQ,GAAAd,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAE1DG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAgJ,CAAhJX,YAAA,CAAgJ6B,qBAAA;QAAnIxB,GAAG,EAAC,eAAe;oBAAUF,KAAA,CAAAC,QAAQ,CAAC0B,WAAW;mEAApB3B,KAAA,CAAAC,QAAQ,CAAC0B,WAAW,GAAAhB,MAAA;QAAGiB,MAAM,EAAEZ,IAAA,CAAAa,YAAY;QAAKC,OAAO,EAAEd,IAAA,CAAAc,OAAO;QAAGC,QAAM,EAAEC,QAAA,CAAAC;;;QAIrHpC,YAAA,CAGeS,uBAAA;wBAFf,MAAgH,CAAhHT,YAAA,CAAgHqC,oBAAA;QAArGC,IAAI,EAAC,SAAS;QAACtB,IAAI,EAAC,OAAO;QAAEuB,OAAK,EAAEJ,QAAA,CAAAK,IAAI;QAAGC,OAAO,EAAEtC,KAAA,CAAAuC,UAAU;QAAEC,IAAI,EAAC;;0BAAiB,MAAG,C;;iDACpG3C,YAAA,CAAuFqC,oBAAA;QAA5EC,IAAI,EAAC,MAAM;QAACtB,IAAI,EAAC,OAAO;QAAEuB,OAAK,EAAEJ,QAAA,CAAAS,MAAM;QAAED,IAAI,EAAC;;0BAAe,MAAG,C"}]}