package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/registerinfo")
public class RegisterinfoController{
	
	@Resource
	private RegisterinfoService registerinfoService;
	
	//返校登记列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Registerinfo>> list(@RequestBody Registerinfo registerinfo, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = registerinfoService.getCount(registerinfo);
		//获取当前页记录
		List<Registerinfo> registerinfoList = registerinfoService.queryRegisterinfoList(registerinfo, page);
		//遍历
		for (Registerinfo registerinfo2 : registerinfoList) {
			registerinfo2.setNote(removeHTML.Html2Text(registerinfo2.getNote()));

		}

		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(registerinfoList, counts, page_count);
	}
        
	//添加返校登记
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Registerinfo registerinfo, HttpServletRequest req) throws Exception {
		try {
			registerinfo.setReturndate(removeHTML.USAToChina1(registerinfo.getReturndate()));
			registerinfoService.insertRegisterinfo(registerinfo); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除返校登记
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			registerinfoService.deleteRegisterinfo(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改返校登记
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Registerinfo registerinfo, HttpServletRequest req) throws Exception {
		try {
			registerinfoService.updateRegisterinfo(registerinfo); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回返校登记详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Registerinfo registerinfo=registerinfoService.queryRegisterinfoById(id); //根据ID查询
			return Response.success(registerinfo);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
}

