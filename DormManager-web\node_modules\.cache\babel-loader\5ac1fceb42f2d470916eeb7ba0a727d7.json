{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total2.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total2.vue", "mtime": 1749044636470}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "echarts", "data", "pieData", "pieName", "mounted", "getdata", "window", "addEventListener", "resizeChart", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "url", "para", "post", "then", "res", "code", "ss", "resdata", "pieName2", "pieData2", "i", "length", "name", "num", "initEcharts", "$message", "error", "msg", "dom", "document", "getElementById", "myChart", "init", "renderer", "option", "title", "tooltip", "trigger", "axisPointer", "type", "legend", "grid", "left", "right", "bottom", "containLabel", "xAxis", "boundaryGap", "yAxis", "series", "setOption", "getInstanceByDom", "resize"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\total\\Total2.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n       <div id=\"container\" :style=\"{ height: '600px' }\"></div>\n \n    </div>\n</template>\n\n<script>\n\n import request, { base } from \"../../../../utils/http\";\n import * as echarts from 'echarts';\n \n export default {\n   data() {\n     return {\n       pieData: [],\n       pieName: [],\n     };\n   },\n \n   mounted() {\n     this.getdata();\n     window.addEventListener('resize', this.resizeChart);\n   },\n   beforeDestroy() {\n     window.removeEventListener('resize', this.resizeChart);\n   },\n   methods: {\n     // 数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport3\";\n \n       let para = {};\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n           var pieName2 = [];\n           var pieData2 = [];\n           for (let i = 0; i < ss.length; i++) {\n             pieName2[i] = ss[i].name;\n             pieData2[i] = ss[i].num;\n           }\n           this.pieName = pieName2;\n           this.pieData = pieData2;\n           this.initEcharts();\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n     },\n \n     initEcharts() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.init(dom, null, { renderer: 'canvas' });\n \n       const option = {\n         title: {\n \n         },\n         tooltip: {\n           trigger: 'axis',\n           axisPointer: {\n             type: 'shadow'\n           }\n         },\n         legend: {},\n         grid: {\n           left: '3%',\n           right: '4%',\n           bottom: '3%',\n           containLabel: true\n         },\n         xAxis: {\n           type: 'value',\n           boundaryGap: [0, 0.01]\n         },\n         yAxis: {\n           type: 'category',\n           data: this.pieName\n         },\n         series: [\n           {\n             name: '总销售数量',\n             type: 'bar',\n             data: this.pieData\n           }\n         ]\n       };\n \n       if (option && typeof option === 'object') {\n         myChart.setOption(option);\n       }\n     },\n     resizeChart() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.getInstanceByDom(dom);\n       myChart.resize();\n     }\n   }\n };\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": "AASC,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAO,KAAKC,OAAM,MAAO,SAAS;AAElC,eAAe;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,OAAO,CAAC,CAAC;IACdC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,WAAW,CAAC;EACrD,CAAC;EACDC,aAAaA,CAAA,EAAG;IACdH,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,WAAW,CAAC;EACxD,CAAC;EACDG,OAAO,EAAE;IACP;IACAN,OAAOA,CAAA,EAAG;MACR,IAAIO,GAAE,GAAIb,IAAG,GAAI,0BAA0B;MAE3C,IAAIc,IAAG,GAAI,CAAC,CAAC;MAEbf,OAAO,CAACgB,IAAI,CAACF,GAAG,EAAEC,IAAI,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAIC,EAAC,GAAIF,GAAG,CAACG,OAAO;UACpB,IAAIC,QAAO,GAAI,EAAE;UACjB,IAAIC,QAAO,GAAI,EAAE;UACjB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIJ,EAAE,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;YAClCF,QAAQ,CAACE,CAAC,IAAIJ,EAAE,CAACI,CAAC,CAAC,CAACE,IAAI;YACxBH,QAAQ,CAACC,CAAC,IAAIJ,EAAE,CAACI,CAAC,CAAC,CAACG,GAAG;UACzB;UACA,IAAI,CAACtB,OAAM,GAAIiB,QAAQ;UACvB,IAAI,CAAClB,OAAM,GAAImB,QAAQ;UACvB,IAAI,CAACK,WAAW,CAAC,CAAC;QACpB,OAAO;UACL,IAAI,CAACC,QAAQ,CAACC,KAAK,CAACZ,GAAG,CAACa,GAAG,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC;IAEDH,WAAWA,CAAA,EAAG;MACZ,MAAMI,GAAE,GAAIC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC;MAChD,MAAMC,OAAM,GAAIjC,OAAO,CAACkC,IAAI,CAACJ,GAAG,EAAE,IAAI,EAAE;QAAEK,QAAQ,EAAE;MAAS,CAAC,CAAC;MAE/D,MAAMC,MAAK,GAAI;QACbC,KAAK,EAAE,CAEP,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXC,IAAI,EAAE;UACR;QACF,CAAC;QACDC,MAAM,EAAE,CAAC,CAAC;QACVC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,IAAI;UACZC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLP,IAAI,EAAE,OAAO;UACbQ,WAAW,EAAE,CAAC,CAAC,EAAE,IAAI;QACvB,CAAC;QACDC,KAAK,EAAE;UACLT,IAAI,EAAE,UAAU;UAChBxC,IAAI,EAAE,IAAI,CAACE;QACb,CAAC;QACDgD,MAAM,EAAE,CACN;UACE3B,IAAI,EAAE,OAAO;UACbiB,IAAI,EAAE,KAAK;UACXxC,IAAI,EAAE,IAAI,CAACC;QACb;MAEJ,CAAC;MAED,IAAIkC,MAAK,IAAK,OAAOA,MAAK,KAAM,QAAQ,EAAE;QACxCH,OAAO,CAACmB,SAAS,CAAChB,MAAM,CAAC;MAC3B;IACF,CAAC;IACD5B,WAAWA,CAAA,EAAG;MACZ,MAAMsB,GAAE,GAAIC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC;MAChD,MAAMC,OAAM,GAAIjC,OAAO,CAACqD,gBAAgB,CAACvB,GAAG,CAAC;MAC7CG,OAAO,CAACqB,MAAM,CAAC,CAAC;IAClB;EACF;AACF,CAAC"}]}