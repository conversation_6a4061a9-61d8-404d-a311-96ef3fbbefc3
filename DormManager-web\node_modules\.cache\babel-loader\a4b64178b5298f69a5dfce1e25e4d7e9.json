{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue?vue&type=template&id=05e4597b&scoped=true", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue", "mtime": 1749047102541}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\babel.config.js", "mtime": 1749034452000}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749043013784}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlQ29tbWVudFZOb2RlIGFzIF9jcmVhdGVDb21tZW50Vk5vZGUsIGNyZWF0ZUVsZW1lbnRWTm9kZSBhcyBfY3JlYXRlRWxlbWVudFZOb2RlLCB0b0Rpc3BsYXlTdHJpbmcgYXMgX3RvRGlzcGxheVN0cmluZywgY3JlYXRlVGV4dFZOb2RlIGFzIF9jcmVhdGVUZXh0Vk5vZGUsIHJlc29sdmVDb21wb25lbnQgYXMgX3Jlc29sdmVDb21wb25lbnQsIHdpdGhDdHggYXMgX3dpdGhDdHgsIGNyZWF0ZVZOb2RlIGFzIF9jcmVhdGVWTm9kZSwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrLCByZW5kZXJMaXN0IGFzIF9yZW5kZXJMaXN0LCBGcmFnbWVudCBhcyBfRnJhZ21lbnQsIGNyZWF0ZUJsb2NrIGFzIF9jcmVhdGVCbG9jaywgdlNob3cgYXMgX3ZTaG93LCB3aXRoRGlyZWN0aXZlcyBhcyBfd2l0aERpcmVjdGl2ZXMsIHJlc29sdmVEaXJlY3RpdmUgYXMgX3Jlc29sdmVEaXJlY3RpdmUsIHB1c2hTY29wZUlkIGFzIF9wdXNoU2NvcGVJZCwgcG9wU2NvcGVJZCBhcyBfcG9wU2NvcGVJZCB9IGZyb20gInZ1ZSI7CmNvbnN0IF93aXRoU2NvcGVJZCA9IG4gPT4gKF9wdXNoU2NvcGVJZCgiZGF0YS12LTA1ZTQ1OTdiIiksIG4gPSBuKCksIF9wb3BTY29wZUlkKCksIG4pOwpjb25zdCBfaG9pc3RlZF8xID0gLyojX19QVVJFX18qL193aXRoU2NvcGVJZCgoKSA9PiAvKiNfX1BVUkVfXyovX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogIHNsb3Q6ICJoZWFkZXIiLAogIGNsYXNzOiAiY2xlYXJmaXgiCn0sIFsvKiNfX1BVUkVfXyovX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIHsKICBzdHlsZTogewogICAgImZvbnQtc2l6ZSI6ICIxOHB4IiwKICAgICJmb250LXdlaWdodCI6ICJib2xkIgogIH0KfSwgIuaIkeeahOWuv+iIjeS/oeaBryIpXSwgLTEgLyogSE9JU1RFRCAqLykpOwpjb25zdCBfaG9pc3RlZF8yID0gewogIGtleTogMAp9Owpjb25zdCBfaG9pc3RlZF8zID0gLyojX19QVVJFX18qL193aXRoU2NvcGVJZCgoKSA9PiAvKiNfX1BVUkVfXyovX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3Ryb25nIiwgbnVsbCwgIuWuv+iIjealvO+8miIsIC0xIC8qIEhPSVNURUQgKi8pKTsKY29uc3QgX2hvaXN0ZWRfNCA9IC8qI19fUFVSRV9fKi9fd2l0aFNjb3BlSWQoKCkgPT4gLyojX19QVVJFX18qL19jcmVhdGVFbGVtZW50Vk5vZGUoInN0cm9uZyIsIG51bGwsICLlrr/oiI3nvJblj7fvvJoiLCAtMSAvKiBIT0lTVEVEICovKSk7CmNvbnN0IF9ob2lzdGVkXzUgPSB7CiAga2V5OiAxCn07CmNvbnN0IF9ob2lzdGVkXzYgPSAvKiNfX1BVUkVfXyovX3dpdGhTY29wZUlkKCgpID0+IC8qI19fUFVSRV9fKi9fY3JlYXRlRWxlbWVudFZOb2RlKCJwIiwgewogIHN0eWxlOiB7CiAgICAiY29sb3IiOiAiIzk5OSIKICB9Cn0sICLmmoLmnKrliIbphY3lrr/oiI0iLCAtMSAvKiBIT0lTVEVEICovKSk7CmNvbnN0IF9ob2lzdGVkXzcgPSBbX2hvaXN0ZWRfNl07CmNvbnN0IF9ob2lzdGVkXzggPSB7CiAgc2xvdDogImhlYWRlciIsCiAgY2xhc3M6ICJjbGVhcmZpeCIKfTsKY29uc3QgX2hvaXN0ZWRfOSA9IC8qI19fUFVSRV9fKi9fd2l0aFNjb3BlSWQoKCkgPT4gLyojX19QVVJFX18qL19jcmVhdGVFbGVtZW50Vk5vZGUoInNwYW4iLCB7CiAgc3R5bGU6IHsKICAgICJmb250LXNpemUiOiAiMThweCIsCiAgICAiZm9udC13ZWlnaHQiOiAiYm9sZCIKICB9Cn0sICLnlLPor7fmm7TmjaLlrr/oiI0iLCAtMSAvKiBIT0lTVEVEICovKSk7CmNvbnN0IF9ob2lzdGVkXzEwID0gLyojX19QVVJFX18qL19jcmVhdGVUZXh0Vk5vZGUoIuaPkOS6pOeUs+ivtyIpOwpjb25zdCBfaG9pc3RlZF8xMSA9IC8qI19fUFVSRV9fKi9fY3JlYXRlVGV4dFZOb2RlKCLph43nva4iKTsKY29uc3QgX2hvaXN0ZWRfMTIgPSB7CiAgc2xvdDogImhlYWRlciIsCiAgY2xhc3M6ICJjbGVhcmZpeCIKfTsKY29uc3QgX2hvaXN0ZWRfMTMgPSAvKiNfX1BVUkVfXyovX3dpdGhTY29wZUlkKCgpID0+IC8qI19fUFVSRV9fKi9fY3JlYXRlRWxlbWVudFZOb2RlKCJzcGFuIiwgewogIHN0eWxlOiB7CiAgICAiZm9udC1zaXplIjogIjE4cHgiLAogICAgImZvbnQtd2VpZ2h0IjogImJvbGQiCiAgfQp9LCAi55Sz6K+35Y6G5Y+yIiwgLTEgLyogSE9JU1RFRCAqLykpOwpjb25zdCBfaG9pc3RlZF8xNCA9IC8qI19fUFVSRV9fKi9fY3JlYXRlVGV4dFZOb2RlKCLliLfmlrAiKTsKY29uc3QgX2hvaXN0ZWRfMTUgPSAvKiNfX1BVUkVfXyovX2NyZWF0ZVRleHRWTm9kZSgi6K+m5oOFIik7CmNvbnN0IF9ob2lzdGVkXzE2ID0gLyojX19QVVJFX18qL19jcmVhdGVUZXh0Vk5vZGUoIuWIoOmZpCIpOwpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogIGNvbnN0IF9jb21wb25lbnRfZWxfY29sID0gX3Jlc29sdmVDb21wb25lbnQoImVsLWNvbCIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfcm93ID0gX3Jlc29sdmVDb21wb25lbnQoImVsLXJvdyIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfY2FyZCA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1jYXJkIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9idXR0b24gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtYnV0dG9uIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9vcHRpb24gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtb3B0aW9uIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9zZWxlY3QgPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtc2VsZWN0Iik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtZm9ybS1pdGVtIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9pbnB1dCA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1pbnB1dCIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfZm9ybSA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1mb3JtIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF90YWJsZV9jb2x1bW4gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtdGFibGUtY29sdW1uIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF90YWcgPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtdGFnIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF90YWJsZSA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC10YWJsZSIpOwogIGNvbnN0IF9kaXJlY3RpdmVfbG9hZGluZyA9IF9yZXNvbHZlRGlyZWN0aXZlKCJsb2FkaW5nIik7CiAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgbnVsbCwgW19jcmVhdGVDb21tZW50Vk5vZGUoIiDlvZPliY3lrr/oiI3kv6Hmga8gIiksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2NhcmQsIHsKICAgIGNsYXNzOiAiYm94LWNhcmQiLAogICAgc3R5bGU6IHsKICAgICAgIm1hcmdpbi1ib3R0b20iOiAiMjBweCIKICAgIH0KICB9LCB7CiAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2hvaXN0ZWRfMSwgJGRhdGEuY3VycmVudERvcm1pdG9yeS5kb3JvID8gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgX2hvaXN0ZWRfMiwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3JvdywgewogICAgICBndXR0ZXI6IDIwCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9jb2wsIHsKICAgICAgICBzcGFuOiAyNAogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVFbGVtZW50Vk5vZGUoInAiLCBudWxsLCBbX2hvaXN0ZWRfMywgX2NyZWF0ZVRleHRWTm9kZShfdG9EaXNwbGF5U3RyaW5nKCRkYXRhLmN1cnJlbnREb3JtaXRvcnkuZGJuYW1lKSwgMSAvKiBURVhUICovKV0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJwIiwgbnVsbCwgW19ob2lzdGVkXzQsIF9jcmVhdGVUZXh0Vk5vZGUoX3RvRGlzcGxheVN0cmluZygkZGF0YS5jdXJyZW50RG9ybWl0b3J5LmRvcm8pLCAxIC8qIFRFWFQgKi8pXSldKSwKCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSldKSwKCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KV0pKSA6IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzUsIF9ob2lzdGVkXzcpKV0pLAogICAgXzogMSAvKiBTVEFCTEUgKi8KICB9KSwgX2NyZWF0ZUNvbW1lbnRWTm9kZSgiIOeUs+ivt+abtOaNouWuv+iIjSAiKSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfY2FyZCwgewogICAgY2xhc3M6ICJib3gtY2FyZCIsCiAgICBzdHlsZTogewogICAgICAibWFyZ2luLWJvdHRvbSI6ICIyMHB4IgogICAgfQogIH0sIHsKICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF84LCBbX2hvaXN0ZWRfOSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICAgIHN0eWxlOiB7CiAgICAgICAgImZsb2F0IjogInJpZ2h0IiwKICAgICAgICAicGFkZGluZyI6ICIzcHggMCIKICAgICAgfSwKICAgICAgdHlwZTogInRleHQiLAogICAgICBvbkNsaWNrOiBfY2FjaGVbMF0gfHwgKF9jYWNoZVswXSA9ICRldmVudCA9PiAkZGF0YS5zaG93QXBwbHlGb3JtID0gISRkYXRhLnNob3dBcHBseUZvcm0pCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVGV4dFZOb2RlKF90b0Rpc3BsYXlTdHJpbmcoJGRhdGEuc2hvd0FwcGx5Rm9ybSA/ICfmlLbotbcnIDogJ+WxleW8gCcpLCAxIC8qIFRFWFQgKi8pXSksCgogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSldKSwgX3dpdGhEaXJlY3RpdmVzKF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIG51bGwsIFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtLCB7CiAgICAgIG1vZGVsOiAkZGF0YS5hcHBseUZvcm0sCiAgICAgIHJ1bGVzOiAkZGF0YS5hcHBseVJ1bGVzLAogICAgICByZWY6ICJhcHBseUZvcm1SZWYiLAogICAgICAibGFiZWwtd2lkdGgiOiAiMTIwcHgiCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIuaWsOWuv+iIjealvCIsCiAgICAgICAgcHJvcDogImRiaWQyIgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3NlbGVjdCwgewogICAgICAgICAgbW9kZWxWYWx1ZTogJGRhdGEuYXBwbHlGb3JtLmRiaWQyLAogICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbMV0gfHwgKF9jYWNoZVsxXSA9ICRldmVudCA9PiAkZGF0YS5hcHBseUZvcm0uZGJpZDIgPSAkZXZlbnQpLAogICAgICAgICAgcGxhY2Vob2xkZXI6ICLor7fpgInmi6nlrr/oiI3mpbwiLAogICAgICAgICAgb25DaGFuZ2U6ICRvcHRpb25zLm9uQnVpbGRpbmdDaGFuZ2UsCiAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAid2lkdGgiOiAiMzAwcHgiCiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gWyhfb3BlbkJsb2NrKHRydWUpLCBfY3JlYXRlRWxlbWVudEJsb2NrKF9GcmFnbWVudCwgbnVsbCwgX3JlbmRlckxpc3QoJGRhdGEuZG9ybWJ1aWxkaW5nTGlzdCwgaXRlbSA9PiB7CiAgICAgICAgICAgIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVCbG9jayhfY29tcG9uZW50X2VsX29wdGlvbiwgewogICAgICAgICAgICAgIGtleTogaXRlbS5kYmlkLAogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLmRibmFtZSwKICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5kYmlkCiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibGFiZWwiLCAidmFsdWUiXSk7CiAgICAgICAgICB9KSwgMTI4IC8qIEtFWUVEX0ZSQUdNRU5UICovKSldKSwKCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSIsICJvbkNoYW5nZSJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIuaWsOWuv+iIjSIsCiAgICAgICAgcHJvcDogImRvcm8yIgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3NlbGVjdCwgewogICAgICAgICAgbW9kZWxWYWx1ZTogJGRhdGEuYXBwbHlGb3JtLmRvcm8yLAogICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbMl0gfHwgKF9jYWNoZVsyXSA9ICRldmVudCA9PiAkZGF0YS5hcHBseUZvcm0uZG9ybzIgPSAkZXZlbnQpLAogICAgICAgICAgcGxhY2Vob2xkZXI6ICLor7flhYjpgInmi6nlrr/oiI3mpbwiLAogICAgICAgICAgZGlzYWJsZWQ6ICEkZGF0YS5hcHBseUZvcm0uZGJpZDIsCiAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAid2lkdGgiOiAiMzAwcHgiCiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gWyhfb3BlbkJsb2NrKHRydWUpLCBfY3JlYXRlRWxlbWVudEJsb2NrKF9GcmFnbWVudCwgbnVsbCwgX3JlbmRlckxpc3QoJGRhdGEuZmlsdGVyZWREb3JtaXRvcnlMaXN0LCBpdGVtID0+IHsKICAgICAgICAgICAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKF9jb21wb25lbnRfZWxfb3B0aW9uLCB7CiAgICAgICAgICAgICAga2V5OiBpdGVtLmRvcm8sCiAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0uZG9ybywKICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5kb3JvCiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibGFiZWwiLCAidmFsdWUiXSk7CiAgICAgICAgICB9KSwgMTI4IC8qIEtFWUVEX0ZSQUdNRU5UICovKSldKSwKCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSIsICJkaXNhYmxlZCJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIueUs+ivt+WOn+WboCIsCiAgICAgICAgcHJvcDogImFwcGxpY2F0aW9ucmVhc29uIgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2lucHV0LCB7CiAgICAgICAgICB0eXBlOiAidGV4dGFyZWEiLAogICAgICAgICAgbW9kZWxWYWx1ZTogJGRhdGEuYXBwbHlGb3JtLmFwcGxpY2F0aW9ucmVhc29uLAogICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbM10gfHwgKF9jYWNoZVszXSA9ICRldmVudCA9PiAkZGF0YS5hcHBseUZvcm0uYXBwbGljYXRpb25yZWFzb24gPSAkZXZlbnQpLAogICAgICAgICAgcGxhY2Vob2xkZXI6ICLor7fovpPlhaXnlLPor7fmm7TmjaLlrr/oiI3nmoTljp/lm6AiLAogICAgICAgICAgcm93czogNCwKICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgICJ3aWR0aCI6ICI1MDBweCIKICAgICAgICAgIH0KICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCBudWxsLCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgICAgICAgdHlwZTogInByaW1hcnkiLAogICAgICAgICAgb25DbGljazogJG9wdGlvbnMuc3VibWl0QXBwbGljYXRpb24sCiAgICAgICAgICBsb2FkaW5nOiAkZGF0YS5idG5Mb2FkaW5nCiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19ob2lzdGVkXzEwXSksCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsib25DbGljayIsICJsb2FkaW5nIl0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9idXR0b24sIHsKICAgICAgICAgIG9uQ2xpY2s6ICRvcHRpb25zLnJlc2V0QXBwbHlGb3JtCiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19ob2lzdGVkXzExXSksCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsib25DbGljayJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pXSksCgogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbCIsICJydWxlcyJdKV0sIDUxMiAvKiBORUVEX1BBVENIICovKSwgW1tfdlNob3csICRkYXRhLnNob3dBcHBseUZvcm1dXSldKSwKICAgIF86IDEgLyogU1RBQkxFICovCiAgfSksIF9jcmVhdGVDb21tZW50Vk5vZGUoIiDnlLPor7fljoblj7IgIiksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2NhcmQsIHsKICAgIGNsYXNzOiAiYm94LWNhcmQiCiAgfSwgewogICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzEyLCBbX2hvaXN0ZWRfMTMsIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgICBzdHlsZTogewogICAgICAgICJmbG9hdCI6ICJyaWdodCIsCiAgICAgICAgInBhZGRpbmciOiAiM3B4IDAiCiAgICAgIH0sCiAgICAgIHR5cGU6ICJ0ZXh0IiwKICAgICAgb25DbGljazogJG9wdGlvbnMuZ2V0QXBwbGljYXRpb25IaXN0b3J5CiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfaG9pc3RlZF8xNF0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSwgOCAvKiBQUk9QUyAqLywgWyJvbkNsaWNrIl0pXSksIF93aXRoRGlyZWN0aXZlcyhfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF90YWJsZSwgewogICAgICBkYXRhOiAkZGF0YS5hcHBsaWNhdGlvbkxpc3QsCiAgICAgIHN0eWxlOiB7CiAgICAgICAgIndpZHRoIjogIjEwMCUiCiAgICAgIH0KICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3RhYmxlX2NvbHVtbiwgewogICAgICAgIHByb3A6ICJzdWJtaXNzaW9udGltZSIsCiAgICAgICAgbGFiZWw6ICLnlLPor7fml7bpl7QiLAogICAgICAgIHdpZHRoOiAiMTUwIgogICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfdGFibGVfY29sdW1uLCB7CiAgICAgICAgbGFiZWw6ICLljp/lrr/oiI0iLAogICAgICAgIHdpZHRoOiAiMTUwIgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoc2NvcGUgPT4gW19jcmVhdGVUZXh0Vk5vZGUoX3RvRGlzcGxheVN0cmluZyhzY29wZS5yb3cuZGJuYW1lIHx8ICflrr/oiI3mpbwnICsgc2NvcGUucm93LmRiaWQpICsgIiAtICIgKyBfdG9EaXNwbGF5U3RyaW5nKHNjb3BlLnJvdy5kb3JvKSwgMSAvKiBURVhUICovKV0pLAoKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfdGFibGVfY29sdW1uLCB7CiAgICAgICAgbGFiZWw6ICLnm67moIflrr/oiI0iLAogICAgICAgIHdpZHRoOiAiMTUwIgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoc2NvcGUgPT4gW19jcmVhdGVUZXh0Vk5vZGUoX3RvRGlzcGxheVN0cmluZyhzY29wZS5yb3cuZGJuYW1lMiB8fCAn5a6/6IiN5qW8JyArIHNjb3BlLnJvdy5kYmlkMikgKyAiIC0gIiArIF90b0Rpc3BsYXlTdHJpbmcoc2NvcGUucm93LmRvcm8yKSwgMSAvKiBURVhUICovKV0pLAoKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfdGFibGVfY29sdW1uLCB7CiAgICAgICAgcHJvcDogImFwcGxpY2F0aW9ucmVhc29uIiwKICAgICAgICBsYWJlbDogIueUs+ivt+WOn+WboCIsCiAgICAgICAgInNob3ctb3ZlcmZsb3ctdG9vbHRpcCI6ICIiCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF90YWJsZV9jb2x1bW4sIHsKICAgICAgICBwcm9wOiAicmV2aWV3c3RhdHVzIiwKICAgICAgICBsYWJlbDogIuWuoeaguOeKtuaAgSIsCiAgICAgICAgd2lkdGg6ICIxMDAiCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eChzY29wZSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfdGFnLCB7CiAgICAgICAgICB0eXBlOiAkb3B0aW9ucy5nZXRTdGF0dXNUeXBlKHNjb3BlLnJvdy5yZXZpZXdzdGF0dXMpCiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoX3RvRGlzcGxheVN0cmluZyhzY29wZS5yb3cucmV2aWV3c3RhdHVzKSwgMSAvKiBURVhUICovKV0pLAoKICAgICAgICAgIF86IDIgLyogRFlOQU1JQyAqLwogICAgICAgIH0sIDEwMzIgLyogUFJPUFMsIERZTkFNSUNfU0xPVFMgKi8sIFsidHlwZSJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF90YWJsZV9jb2x1bW4sIHsKICAgICAgICBsYWJlbDogIuaTjeS9nCIsCiAgICAgICAgIm1pbi13aWR0aCI6ICIyNTAiLAogICAgICAgIGFsaWduOiAiY2VudGVyIgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoc2NvcGUgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgICAgICAgdHlwZTogInByaW1hcnkiLAogICAgICAgICAgc2l6ZTogIm1pbmkiLAogICAgICAgICAgb25DbGljazogJGV2ZW50ID0+ICRvcHRpb25zLmhhbmRsZVNob3coc2NvcGUuJGluZGV4LCBzY29wZS5yb3cpLAogICAgICAgICAgaWNvbjogImVsLWljb24tem9vbS1pbiIsCiAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAicGFkZGluZyI6ICIzcHggNnB4IDNweCA2cHgiCiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19ob2lzdGVkXzE1XSksCiAgICAgICAgICBfOiAyIC8qIERZTkFNSUMgKi8KICAgICAgICB9LCAxMDMyIC8qIFBST1BTLCBEWU5BTUlDX1NMT1RTICovLCBbIm9uQ2xpY2siXSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgICAgICAgdHlwZTogImRhbmdlciIsCiAgICAgICAgICBzaXplOiAibWluaSIsCiAgICAgICAgICBvbkNsaWNrOiAkZXZlbnQgPT4gJG9wdGlvbnMuaGFuZGxlRGVsZXRlKHNjb3BlLiRpbmRleCwgc2NvcGUucm93KSwKICAgICAgICAgIGljb246ICJlbC1pY29uLWRlbGV0ZSIsCiAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAicGFkZGluZyI6ICIzcHggNnB4IDNweCA2cHgiCiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19ob2lzdGVkXzE2XSksCiAgICAgICAgICBfOiAyIC8qIERZTkFNSUMgKi8KICAgICAgICB9LCAxMDMyIC8qIFBST1BTLCBEWU5BTUlDX1NMT1RTICovLCBbIm9uQ2xpY2siXSldKSwKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9KV0pLAoKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0sIDggLyogUFJPUFMgKi8sIFsiZGF0YSJdKSwgW1tfZGlyZWN0aXZlX2xvYWRpbmcsICRkYXRhLmxpc3RMb2FkaW5nXV0pXSksCiAgICBfOiAxIC8qIFNUQUJMRSAqLwogIH0pXSk7Cn0="}, {"version": 3, "names": ["_createElementVNode", "slot", "class", "style", "_hoisted_6", "_createElementBlock", "_createCommentVNode", "_createVNode", "_component_el_card", "_hoisted_1", "$data", "currentDormitory", "doro", "_hoisted_2", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_3", "dbname", "_hoisted_4", "_hoisted_5", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_component_el_button", "type", "onClick", "_cache", "$event", "showApplyForm", "_component_el_form", "model", "applyForm", "rules", "applyRules", "ref", "_component_el_form_item", "label", "prop", "_component_el_select", "dbid2", "placeholder", "onChange", "$options", "onBuildingChange", "_Fragment", "_renderList", "dormbuildingList", "item", "_createBlock", "_component_el_option", "key", "dbid", "value", "doro2", "disabled", "filteredDormitoryList", "_component_el_input", "<PERSON><PERSON><PERSON>", "rows", "submitApplication", "loading", "btnLoading", "resetApplyForm", "_hoisted_12", "_hoisted_13", "getApplicationHistory", "_component_el_table", "data", "applicationList", "_component_el_table_column", "width", "default", "_withCtx", "scope", "row", "_toDisplayString", "dbname2", "_component_el_tag", "getStatusType", "reviewstatus", "align", "size", "handleShow", "$index", "icon", "handleDelete", "listLoading"], "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\student\\MyDormitory.vue"], "sourcesContent": ["<template>\n  <div>\n    <!-- 当前宿舍信息 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">我的宿舍信息</span>\n      </div>\n      <div v-if=\"currentDormitory.doro\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <p><strong>宿舍楼：</strong>{{ currentDormitory.dbname }}</p>\n            <p><strong>宿舍编号：</strong>{{ currentDormitory.doro }}</p>\n          </el-col>\n       \n        </el-row>\n      </div>\n      <div v-else>\n        <p style=\"color: #999;\">暂未分配宿舍</p>\n      </div>\n    </el-card>\n\n    <!-- 申请更换宿舍 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请更换宿舍</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"showApplyForm = !showApplyForm\">\n          {{ showApplyForm ? '收起' : '展开' }}\n        </el-button>\n      </div>\n      <div v-show=\"showApplyForm\">\n        <el-form :model=\"applyForm\" :rules=\"applyRules\" ref=\"applyFormRef\" label-width=\"120px\">\n          <el-form-item label=\"新宿舍楼\" prop=\"dbid2\">\n            <el-select v-model=\"applyForm.dbid2\" placeholder=\"请选择宿舍楼\" @change=\"onBuildingChange\" style=\"width: 300px;\">\n              <el-option v-for=\"item in dormbuildingList\" :key=\"item.dbid\" :label=\"item.dbname\" :value=\"item.dbid\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"新宿舍\" prop=\"doro2\">\n            <el-select v-model=\"applyForm.doro2\" placeholder=\"请先选择宿舍楼\" :disabled=\"!applyForm.dbid2\" style=\"width: 300px;\">\n              <el-option v-for=\"item in filteredDormitoryList\" :key=\"item.doro\" :label=\"item.doro\" :value=\"item.doro\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"申请原因\" prop=\"applicationreason\">\n            <el-input type=\"textarea\" v-model=\"applyForm.applicationreason\" placeholder=\"请输入申请更换宿舍的原因\" :rows=\"4\" style=\"width: 500px;\"></el-input>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"submitApplication\" :loading=\"btnLoading\">提交申请</el-button>\n            <el-button @click=\"resetApplyForm\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n    </el-card>\n\n    <!-- 申请历史 -->\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">申请历史</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"getApplicationHistory\">刷新</el-button>\n      </div>\n      <el-table :data=\"applicationList\" style=\"width: 100%\" v-loading=\"listLoading\">\n        <el-table-column prop=\"submissiontime\" label=\"申请时间\" width=\"150\"></el-table-column>\n        <el-table-column label=\"原宿舍\" width=\"150\">\n          <template #default=\"scope\">\n            {{ scope.row.dbname || '宿舍楼' + scope.row.dbid }} - {{ scope.row.doro }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"目标宿舍\" width=\"150\">\n          <template #default=\"scope\">\n            {{ scope.row.dbname2 || '宿舍楼' + scope.row.dbid2 }} - {{ scope.row.doro2 }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"applicationreason\" label=\"申请原因\" show-overflow-tooltip></el-table-column>\n        <el-table-column prop=\"reviewstatus\" label=\"审核状态\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getStatusType(scope.row.reviewstatus)\">{{ scope.row.reviewstatus }}</el-tag>\n          </template>\n        </el-table-column>\n<el-table-column label=\"操作\" min-width=\"250\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n      \n      </el-table>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'MyDormitory',\n  data() {\n    return {\n      currentDormitory: {}, // 当前宿舍信息\n      showApplyForm: false, // 是否显示申请表单\n      btnLoading: false, // 提交按钮加载状态\n      listLoading: false, // 列表加载状态\n      \n      // 申请表单\n      applyForm: {\n        sno: '',\n        dbid: null, // 原宿舍楼ID\n        doro: '', // 原宿舍编号\n        dbid2: null, // 新宿舍楼ID\n        doro2: '', // 新宿舍编号\n        applicationreason: ''\n      },\n      \n      // 表单验证规则\n      applyRules: {\n        dbid2: [{ required: true, message: '请选择新宿舍楼', trigger: 'change' }],\n        doro2: [{ required: true, message: '请选择新宿舍', trigger: 'change' }],\n        applicationreason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]\n      },\n      \n      // 数据列表\n      dormbuildingList: [], // 宿舍楼列表\n      filteredDormitoryList: [], // 过滤后的宿舍列表\n      applicationList: [], // 申请历史列表\n      \n      // 学生信息\n      studentInfo: {}\n    };\n  },\n  \n  created() {\n    this.getStudentInfo();\n    this.getDormbuildingList();\n    this.getApplicationHistory();\n  },\n  \n  methods: {\n    // 获取学生信息和当前宿舍信息\n    getStudentInfo() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.studentInfo = user;\n      this.applyForm.sno = user.sno;\n\n      let url = base + \"/student/get?id=\" + user.sno;\n      request.post(url, {}).then((res) => {\n        if (res.code == 200) {\n          this.currentDormitory = res.resdata;\n          // 设置原宿舍信息\n          this.applyForm.dbid = res.resdata.dbid;\n          this.applyForm.doro = res.resdata.doro;\n        }\n      });\n    },\n    \n    // 获取宿舍楼列表\n    getDormbuildingList() {\n      let url = base + \"/dormbuilding/list?currentPage=1&pageSize=1000\";\n      request.post(url, {}).then((res) => {\n        this.dormbuildingList = res.resdata;\n      });\n    },\n    \n    // 宿舍楼变化时的处理\n    onBuildingChange() {\n      this.applyForm.doro2 = ''; // 清空宿舍选择\n      this.updateDormitoryList();\n    },\n    \n    // 更新宿舍列表（根据宿舍楼和学生性别过滤）\n    updateDormitoryList() {\n      if (!this.applyForm.dbid2 || !this.studentInfo.sex) {\n        this.filteredDormitoryList = [];\n        return;\n      }\n\n      let para = {\n        dbid: this.applyForm.dbid2,\n        dorgender: this.studentInfo.sex\n      };\n      let url = base + \"/dormitory/listByBuildingAndGender\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          // 过滤掉当前宿舍\n          this.filteredDormitoryList = res.resdata.filter(item => \n            !(item.dbid == this.applyForm.dbid && item.doro == this.applyForm.doro)\n          );\n        } else {\n          this.filteredDormitoryList = [];\n          this.$message({\n            message: res.msg || \"获取宿舍列表失败\",\n            type: \"error\"\n          });\n        }\n      }).catch(() => {\n        this.filteredDormitoryList = [];\n        this.$message({\n          message: \"获取宿舍列表失败\",\n          type: \"error\"\n        });\n      });\n    },\n    \n    // 提交申请\n    submitApplication() {\n      this.$refs.applyFormRef.validate((valid) => {\n        if (valid) {\n          // 检查是否选择了不同的宿舍\n          if (this.applyForm.dbid == this.applyForm.dbid2 && this.applyForm.doro == this.applyForm.doro2) {\n            this.$message({\n              message: \"新宿舍不能与当前宿舍相同\",\n              type: \"warning\"\n            });\n            return;\n          }\n          \n          this.btnLoading = true;\n          let url = base + \"/dormitorychange/apply\";\n          request.post(url, this.applyForm).then((res) => {\n            if (res.code == 200) {\n              this.$message({\n                message: \"申请提交成功，请等待审核\",\n                type: \"success\"\n              });\n              this.resetApplyForm();\n              this.showApplyForm = false;\n              this.getApplicationHistory(); // 刷新申请历史\n            } else {\n              this.$message({\n                message: res.msg || \"申请提交失败\",\n                type: \"error\"\n              });\n            }\n            this.btnLoading = false;\n          }).catch(() => {\n            this.$message({\n              message: \"申请提交失败\",\n              type: \"error\"\n            });\n            this.btnLoading = false;\n          });\n        }\n      });\n    },\n    \n    // 重置申请表单\n    resetApplyForm() {\n      this.$refs.applyFormRef.resetFields();\n      this.applyForm.dbid2 = null;\n      this.applyForm.doro2 = '';\n      this.applyForm.applicationreason = '';\n      this.filteredDormitoryList = [];\n    },\n    \n    // 获取申请历史\n    getApplicationHistory() {\n      this.listLoading = true;\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      let para = {\n        sno: user ? user.sno : this.studentInfo.sno\n      };\n      let url = base + \"/dormitorychange/list?currentPage=1&pageSize=100\";\n      request.post(url, para).then((res) => {\n        if (res.code == 200) {\n          this.applicationList = res.resdata || [];\n        }\n        this.listLoading = false;\n      }).catch(() => {\n        this.listLoading = false;\n      });\n    },\n\n         // 删除宿舍更换\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/dormitorychange/del?id=\" + row.id;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n    },\n\n             \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/DormitorychangeDetail\",\n             query: {\n                id: row.id,\n              },\n          });\n        },\n    \n    // 获取状态标签类型\n    getStatusType(status) {\n      switch (status) {\n        case '待审核':\n          return 'warning';\n        case '审核通过':\n          return 'success';\n        case '审核不通过':\n          return 'danger';\n        default:\n          return 'info';\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin-bottom: 20px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n</style>\n"], "mappings": ";;gEAIMA,mBAAA,CAEM;EAFDC,IAAI,EAAC,QAAQ;EAACC,KAAK,EAAC;iBACvBF,mBAAA,CAA+D;EAAzDG,KAA2C,EAA3C;IAAA;IAAA;EAAA;AAA2C,GAAC,QAAM,E;;;;gEAKjDH,mBAAA,CAAqB,gBAAb,MAAI;gEACZA,mBAAA,CAAsB,gBAAd,OAAK;;;;gEAMpBA,mBAAA,CAAkC;EAA/BG,KAAoB,EAApB;IAAA;EAAA;AAAoB,GAAC,QAAM;oBAA9BC,UAAkC,C;;EAM/BH,IAAI,EAAC,QAAQ;EAACC,KAAK,EAAC;;gEACvBF,mBAAA,CAA+D;EAAzDG,KAA2C,EAA3C;IAAA;IAAA;EAAA;AAA2C,GAAC,QAAM;kDAqBuB,MAAI;kDAC5C,IAAE;;EAQtCF,IAAI,EAAC,QAAQ;EAACC,KAAK,EAAC;;iEACvBF,mBAAA,CAA6D;EAAvDG,KAA2C,EAA3C;IAAA;IAAA;EAAA;AAA2C,GAAC,MAAI;kDACqC,IAAE;kDAsBwC,IAAE;kDACF,IAAE;;;;;;;;;;;;;;;uBA9E7IE,mBAAA,CAoFM,cAnFJC,mBAAA,YAAe,EACfC,YAAA,CAgBUC,kBAAA;IAhBDN,KAAK,EAAC,UAAU;IAACC,KAA4B,EAA5B;MAAA;IAAA;;sBACxB,MAEM,CAFNM,UAEM,EACKC,KAAA,CAAAC,gBAAgB,CAACC,IAAI,I,cAAhCP,mBAAA,CAQM,OAAAQ,UAAA,GAPJN,YAAA,CAMSO,iBAAA;MANAC,MAAM,EAAE;IAAE;wBACjB,MAGS,CAHTR,YAAA,CAGSS,iBAAA;QAHAC,IAAI,EAAE;MAAE;0BACf,MAAyD,CAAzDjB,mBAAA,CAAyD,YAAtDkB,UAAqB,E,kCAAGR,KAAA,CAAAC,gBAAgB,CAACQ,MAAM,iB,GAClDnB,mBAAA,CAAwD,YAArDoB,UAAsB,E,kCAAGV,KAAA,CAAAC,gBAAgB,CAACC,IAAI,iB;;;;;;2BAKvDP,mBAAA,CAEM,OAAAgB,UAAA,EAAAC,UAAA,G;;MAGRhB,mBAAA,YAAe,EACfC,YAAA,CA4BUC,kBAAA;IA5BDN,KAAK,EAAC,UAAU;IAACC,KAA4B,EAA5B;MAAA;IAAA;;sBACxB,MAKM,CALNH,mBAAA,CAKM,OALNuB,UAKM,GAJJC,UAA+D,EAC/DjB,YAAA,CAEYkB,oBAAA;MAFDtB,KAAoC,EAApC;QAAA;QAAA;MAAA,CAAoC;MAACuB,IAAI,EAAC,MAAM;MAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEnB,KAAA,CAAAoB,aAAa,IAAIpB,KAAA,CAAAoB,aAAa;;wBAChG,MAAiC,C,kCAA9BpB,KAAA,CAAAoB,aAAa,+B;;;0BAGpB9B,mBAAA,CAoBM,cAnBJO,YAAA,CAkBUwB,kBAAA;MAlBAC,KAAK,EAAEtB,KAAA,CAAAuB,SAAS;MAAGC,KAAK,EAAExB,KAAA,CAAAyB,UAAU;MAAEC,GAAG,EAAC,cAAc;MAAC,aAAW,EAAC;;wBAC7E,MAIe,CAJf7B,YAAA,CAIe8B,uBAAA;QAJDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAEY,CAFZhC,YAAA,CAEYiC,oBAAA;sBAFQ9B,KAAA,CAAAuB,SAAS,CAACQ,KAAK;qEAAf/B,KAAA,CAAAuB,SAAS,CAACQ,KAAK,GAAAZ,MAAA;UAAEa,WAAW,EAAC,QAAQ;UAAEC,QAAM,EAAEC,QAAA,CAAAC,gBAAgB;UAAE1C,KAAqB,EAArB;YAAA;UAAA;;4BACxE,MAAgC,E,kBAA3CE,mBAAA,CAAiHyC,SAAA,QAAAC,WAAA,CAAvFrC,KAAA,CAAAsC,gBAAgB,EAAxBC,IAAI;iCAAtBC,YAAA,CAAiHC,oBAAA;cAApEC,GAAG,EAAEH,IAAI,CAACI,IAAI;cAAGf,KAAK,EAAEW,IAAI,CAAC9B,MAAM;cAAGmC,KAAK,EAAEL,IAAI,CAACI;;;;;;;UAGnG9C,YAAA,CAIe8B,uBAAA;QAJDC,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC;;0BAC7B,MAEY,CAFZhC,YAAA,CAEYiC,oBAAA;sBAFQ9B,KAAA,CAAAuB,SAAS,CAACsB,KAAK;qEAAf7C,KAAA,CAAAuB,SAAS,CAACsB,KAAK,GAAA1B,MAAA;UAAEa,WAAW,EAAC,SAAS;UAAEc,QAAQ,GAAG9C,KAAA,CAAAuB,SAAS,CAACQ,KAAK;UAAEtC,KAAqB,EAArB;YAAA;UAAA;;4BAC3E,MAAqC,E,kBAAhDE,mBAAA,CAAoHyC,SAAA,QAAAC,WAAA,CAA1FrC,KAAA,CAAA+C,qBAAqB,EAA7BR,IAAI;iCAAtBC,YAAA,CAAoHC,oBAAA;cAAlEC,GAAG,EAAEH,IAAI,CAACrC,IAAI;cAAG0B,KAAK,EAAEW,IAAI,CAACrC,IAAI;cAAG0C,KAAK,EAAEL,IAAI,CAACrC;;;;;;;UAGtGL,YAAA,CAEe8B,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAAsI,CAAtIhC,YAAA,CAAsImD,mBAAA;UAA5HhC,IAAI,EAAC,UAAU;sBAAUhB,KAAA,CAAAuB,SAAS,CAAC0B,iBAAiB;qEAA3BjD,KAAA,CAAAuB,SAAS,CAAC0B,iBAAiB,GAAA9B,MAAA;UAAEa,WAAW,EAAC,cAAc;UAAEkB,IAAI,EAAE,CAAC;UAAEzD,KAAqB,EAArB;YAAA;UAAA;;;UAEvGI,YAAA,CAGe8B,uBAAA;0BAFb,MAA2F,CAA3F9B,YAAA,CAA2FkB,oBAAA;UAAhFC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAEiB,QAAA,CAAAiB,iBAAiB;UAAGC,OAAO,EAAEpD,KAAA,CAAAqD;;4BAAY,MAAI,C;;mDAC/ExD,YAAA,CAAiDkB,oBAAA;UAArCE,OAAK,EAAEiB,QAAA,CAAAoB;QAAc;4BAAE,MAAE,C;;;;;;;6EAjB9BtD,KAAA,CAAAoB,aAAa,E;;MAuB5BxB,mBAAA,UAAa,EACbC,YAAA,CA+BUC,kBAAA;IA/BDN,KAAK,EAAC;EAAU;sBACvB,MAGM,CAHNF,mBAAA,CAGM,OAHNiE,WAGM,GAFJC,WAA6D,EAC7D3D,YAAA,CAAyGkB,oBAAA;MAA9FtB,KAAoC,EAApC;QAAA;QAAA;MAAA,CAAoC;MAACuB,IAAI,EAAC,MAAM;MAAEC,OAAK,EAAEiB,QAAA,CAAAuB;;wBAAuB,MAAE,C;;sDAE/F5D,YAAA,CAyBW6D,mBAAA;MAzBAC,IAAI,EAAE3D,KAAA,CAAA4D,eAAe;MAAEnE,KAAmB,EAAnB;QAAA;MAAA;;wBAChC,MAAkF,CAAlFI,YAAA,CAAkFgE,0BAAA;QAAjEhC,IAAI,EAAC,gBAAgB;QAACD,KAAK,EAAC,MAAM;QAACkC,KAAK,EAAC;UAC1DjE,YAAA,CAIkBgE,0BAAA;QAJDjC,KAAK,EAAC,KAAK;QAACkC,KAAK,EAAC;;QACtBC,OAAO,EAAAC,QAAA,CAAEC,KAAK,K,kCACpBA,KAAK,CAACC,GAAG,CAACzD,MAAM,YAAYwD,KAAK,CAACC,GAAG,CAACvB,IAAI,IAAG,KAAG,GAAAwB,gBAAA,CAAGF,KAAK,CAACC,GAAG,CAAChE,IAAI,iB;;;UAGxEL,YAAA,CAIkBgE,0BAAA;QAJDjC,KAAK,EAAC,MAAM;QAACkC,KAAK,EAAC;;QACvBC,OAAO,EAAAC,QAAA,CAAEC,KAAK,K,kCACpBA,KAAK,CAACC,GAAG,CAACE,OAAO,YAAYH,KAAK,CAACC,GAAG,CAACnC,KAAK,IAAG,KAAG,GAAAoC,gBAAA,CAAGF,KAAK,CAACC,GAAG,CAACrB,KAAK,iB;;;UAG3EhD,YAAA,CAA+FgE,0BAAA;QAA9EhC,IAAI,EAAC,mBAAmB;QAACD,KAAK,EAAC,MAAM;QAAC,uBAAqB,EAArB;UACvD/B,YAAA,CAIkBgE,0BAAA;QAJDhC,IAAI,EAAC,cAAc;QAACD,KAAK,EAAC,MAAM;QAACkC,KAAK,EAAC;;QAC3CC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvBpE,YAAA,CAA2FwE,iBAAA;UAAlFrD,IAAI,EAAEkB,QAAA,CAAAoC,aAAa,CAACL,KAAK,CAACC,GAAG,CAACK,YAAY;;4BAAG,MAA4B,C,kCAAzBN,KAAK,CAACC,GAAG,CAACK,YAAY,iB;;;;;UAG3F1E,YAAA,CAKkBgE,0BAAA;QALDjC,KAAK,EAAC,IAAI;QAAC,WAAS,EAAC,KAAK;QAAC4C,KAAK,EAAC;;QACvCT,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACzBpE,YAAA,CAA2JkB,oBAAA;UAAhJC,IAAI,EAAC,SAAS;UAACyD,IAAI,EAAC,MAAM;UAAExD,OAAK,EAAAE,MAAA,IAAEe,QAAA,CAAAwC,UAAU,CAACT,KAAK,CAACU,MAAM,EAAEV,KAAK,CAACC,GAAG;UAAGU,IAAI,EAAC,iBAAiB;UAACnF,KAAkC,EAAlC;YAAA;UAAA;;4BAAmC,MAAE,C;;0DAC/II,YAAA,CAA2JkB,oBAAA;UAAhJC,IAAI,EAAC,QAAQ;UAACyD,IAAI,EAAC,MAAM;UAAExD,OAAK,EAAAE,MAAA,IAAEe,QAAA,CAAA2C,YAAY,CAACZ,KAAK,CAACU,MAAM,EAAEV,KAAK,CAACC,GAAG;UAAGU,IAAI,EAAC,gBAAgB;UAACnF,KAAkC,EAAlC;YAAA;UAAA;;4BAAmC,MAAE,C;;;;;;;uDArBxEO,KAAA,CAAA8E,WAAW,E"}]}