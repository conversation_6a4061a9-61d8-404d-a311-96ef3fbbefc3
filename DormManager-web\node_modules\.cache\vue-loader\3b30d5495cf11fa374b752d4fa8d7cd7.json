{"remainingRequest": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeDetail.vue", "mtime": 1749047130658}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749043012919}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749043011899}, {"path": "I:\\product4\\B7839DormManager\\DormManager-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749043013130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgICAgICAgCiAgICAgICAgaW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOwogICAgICAgIGV4cG9ydCBkZWZhdWx0IHsKICAgICAgICAgICAgbmFtZTogJ0Rvcm1pdG9yeWNoYW5nZURldGFpbCcsCiAgICAgICAgICAgIGNvbXBvbmVudHM6IHsKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YSgpIHsKICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgICAgaWQ6ICcnLAogICAgICAgICAgICAgICAgICAgIGZvcm1EYXRhOiB7fSwgLy/ooajljZXmlbDmja4gICAgICAgICAKICAgICAgICAKICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGNyZWF0ZWQoKSB7CiAgICAgICAgICAgICAgICB0aGlzLmlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7IC8v6I635Y+W5Y+C5pWwCiAgICAgICAgICAgICAgICB0aGlzLmdldERhdGFzKCk7CiAgICAgICAgICAgIH0sCiAgICAgICAgCiAgICAgICAgCiAgICAgICAgICAgIG1ldGhvZHM6IHsKICAgICAgICAKICAgICAgICAgICAgICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICAgICAgICAgICAgICBnZXREYXRhcygpIHsKICAgICAgICAgICAgICAgICAgICBsZXQgcGFyYSA9IHsKICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9kb3JtaXRvcnljaGFuZ2UvZ2V0P2lkPSIgKyB0aGlzLmlkOwogICAgICAgICAgICAgICAgICAgIHJlcXVlc3QucG9zdCh1cmwsIHBhcmEpLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcm1EYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyZXMucmVzZGF0YSkpOwogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9LAogICAgICAgIAogICAgICAgICAgICAgICAgLy8g6L+U5ZueCiAgICAgICAgICAgICAgICBiYWNrKCkgewogICAgICAgICAgICAgICAgICAgIC8v6L+U5Zue5LiK5LiA6aG1CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgCiAgICAgICAgICAgIH0sCiAgICAgICAgfQoK"}, {"version": 3, "sources": ["I:\\product4\\B7839DormManager\\DormManager-web\\src\\views\\admin\\dormitorychange\\DormitorychangeDetail.vue"], "names": [], "mappings": ";;QAkCQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACH,CAAC,CAAC,EAAE,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEvB,CAAC;YACL,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;;;YAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;gBAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;oBACX,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;oBACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5B,CAAC,CAAC;gBACN,CAAC;;gBAED,CAAC,EAAE,CAAC;gBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC;;YAEL,CAAC;QACL", "file": "I:/product4/B7839DormManager/DormManager-web/src/views/admin/dormitorychange/DormitorychangeDetail.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n       <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\n<el-form-item label=\"宿舍更换id\">\n{{formData.id}}</el-form-item>\n<el-form-item label=\"学号\">\n{{formData.sno}}</el-form-item>\n<el-form-item label=\"原宿舍楼\">\n{{formData.dbname}}</el-form-item>\n<el-form-item label=\"原宿舍编号\">\n{{formData.doro}}</el-form-item>\n<el-form-item label=\"更换宿舍楼\">\n{{formData.dbname2}}</el-form-item>\n<el-form-item label=\"更换宿舍编号\">\n{{formData.doro2}}</el-form-item>\n<el-form-item label=\"申请原因\">\n{{formData.applicationreason}}</el-form-item>\n<el-form-item label=\"提交时间\">\n{{formData.submissiontime}}</el-form-item>\n<el-form-item label=\"审核状态\">\n{{formData.reviewstatus}}</el-form-item>\n<el-form-item label=\"审核回复\">\n{{formData.reviewresponse}}</el-form-item>\n<el-form-item>\n<el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\n</el-form-item>\n</el-form>\n\n\n    </div>\n</template>\n\n<script>\n        \n        import request, { base } from \"../../../../utils/http\";\n        export default {\n            name: 'DormitorychangeDetail',\n            components: {\n            },\n            data() {\n                return {\n                    id: '',\n                    formData: {}, //表单数据         \n        \n                };\n            },\n            created() {\n                this.id = this.$route.query.id; //获取参数\n                this.getDatas();\n            },\n        \n        \n            methods: {\n        \n                //获取列表数据\n                getDatas() {\n                    let para = {\n                    };\n                    this.listLoading = true;\n                    let url = base + \"/dormitorychange/get?id=\" + this.id;\n                    request.post(url, para).then((res) => {\n                        this.formData = JSON.parse(JSON.stringify(res.resdata));\n                        this.listLoading = false;\n                    });\n                },\n        \n                // 返回\n                back() {\n                    //返回上一页\n                    this.$router.go(-1);\n                },\n        \n            },\n        }\n\n</script>\n<style scoped>\n</style>\n \n\n"]}]}